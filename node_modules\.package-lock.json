{"name": "quran-app-workspace", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"node_modules/quran-app": {"resolved": "quran-app", "link": true}, "quran-app": {"version": "1.0.0", "dependencies": {"expo": "~54.0.12", "expo-av": "^16.0.7", "expo-status-bar": "~3.0.8", "react": "19.1.0", "react-native": "0.81.4"}}, "quran-app/node_modules/@0no-co/graphql.web": {"version": "1.2.0", "license": "MIT", "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0"}, "peerDependenciesMeta": {"graphql": {"optional": true}}}, "quran-app/node_modules/@babel/code-frame": {"version": "7.10.4", "license": "MIT", "dependencies": {"@babel/highlight": "^7.10.4"}}, "quran-app/node_modules/@babel/compat-data": {"version": "7.28.4", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "quran-app/node_modules/@babel/core": {"version": "7.28.4", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.28.3", "@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-module-transforms": "^7.28.3", "@babel/helpers": "^7.28.4", "@babel/parser": "^7.28.4", "@babel/template": "^7.27.2", "@babel/traverse": "^7.28.4", "@babel/types": "^7.28.4", "@jridgewell/remapping": "^2.3.5", "convert-source-map": "^2.0.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.3", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}}, "quran-app/node_modules/@babel/core/node_modules/@babel/code-frame": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "engines": {"node": ">=6.9.0"}}, "quran-app/node_modules/@babel/core/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "quran-app/node_modules/@babel/generator": {"version": "7.28.3", "license": "MIT", "dependencies": {"@babel/parser": "^7.28.3", "@babel/types": "^7.28.2", "@jridgewell/gen-mapping": "^0.3.12", "@jridgewell/trace-mapping": "^0.3.28", "jsesc": "^3.0.2"}, "engines": {"node": ">=6.9.0"}}, "quran-app/node_modules/@babel/helper-annotate-as-pure": {"version": "7.27.3", "license": "MIT", "dependencies": {"@babel/types": "^7.27.3"}, "engines": {"node": ">=6.9.0"}}, "quran-app/node_modules/@babel/helper-compilation-targets": {"version": "7.27.2", "license": "MIT", "dependencies": {"@babel/compat-data": "^7.27.2", "@babel/helper-validator-option": "^7.27.1", "browserslist": "^4.24.0", "lru-cache": "^5.1.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}}, "quran-app/node_modules/@babel/helper-compilation-targets/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "quran-app/node_modules/@babel/helper-create-class-features-plugin": {"version": "7.28.3", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.3", "@babel/helper-member-expression-to-functions": "^7.27.1", "@babel/helper-optimise-call-expression": "^7.27.1", "@babel/helper-replace-supers": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1", "@babel/traverse": "^7.28.3", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "quran-app/node_modules/@babel/helper-create-class-features-plugin/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "quran-app/node_modules/@babel/helper-create-regexp-features-plugin": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "regexpu-core": "^6.2.0", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "quran-app/node_modules/@babel/helper-create-regexp-features-plugin/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "quran-app/node_modules/@babel/helper-define-polyfill-provider": {"version": "0.6.5", "license": "MIT", "dependencies": {"@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-plugin-utils": "^7.27.1", "debug": "^4.4.1", "lodash.debounce": "^4.0.8", "resolve": "^1.22.10"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}}, "quran-app/node_modules/@babel/helper-globals": {"version": "7.28.0", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "quran-app/node_modules/@babel/helper-member-expression-to-functions": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "quran-app/node_modules/@babel/helper-module-imports": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "quran-app/node_modules/@babel/helper-module-transforms": {"version": "7.28.3", "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/traverse": "^7.28.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "quran-app/node_modules/@babel/helper-optimise-call-expression": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "quran-app/node_modules/@babel/helper-plugin-utils": {"version": "7.27.1", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "quran-app/node_modules/@babel/helper-remap-async-to-generator": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-wrap-function": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "quran-app/node_modules/@babel/helper-replace-supers": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-member-expression-to-functions": "^7.27.1", "@babel/helper-optimise-call-expression": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "quran-app/node_modules/@babel/helper-skip-transparent-expression-wrappers": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "quran-app/node_modules/@babel/helper-string-parser": {"version": "7.27.1", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "quran-app/node_modules/@babel/helper-validator-identifier": {"version": "7.27.1", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "quran-app/node_modules/@babel/helper-validator-option": {"version": "7.27.1", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "quran-app/node_modules/@babel/helper-wrap-function": {"version": "7.28.3", "license": "MIT", "dependencies": {"@babel/template": "^7.27.2", "@babel/traverse": "^7.28.3", "@babel/types": "^7.28.2"}, "engines": {"node": ">=6.9.0"}}, "quran-app/node_modules/@babel/helpers": {"version": "7.28.4", "license": "MIT", "dependencies": {"@babel/template": "^7.27.2", "@babel/types": "^7.28.4"}, "engines": {"node": ">=6.9.0"}}, "quran-app/node_modules/@babel/highlight": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.25.9", "chalk": "^2.4.2", "js-tokens": "^4.0.0", "picocolors": "^1.0.0"}, "engines": {"node": ">=6.9.0"}}, "quran-app/node_modules/@babel/parser": {"version": "7.28.4", "license": "MIT", "dependencies": {"@babel/types": "^7.28.4"}, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "quran-app/node_modules/@babel/plugin-proposal-decorators": {"version": "7.28.0", "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/plugin-syntax-decorators": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-proposal-export-default-from": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-syntax-async-generators": {"version": "7.8.4", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-syntax-bigint": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-syntax-class-properties": {"version": "7.12.13", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-syntax-class-static-block": {"version": "7.14.5", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-syntax-decorators": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-syntax-dynamic-import": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-syntax-export-default-from": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-syntax-flow": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-syntax-import-attributes": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-syntax-import-meta": {"version": "7.10.4", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-syntax-json-strings": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-syntax-jsx": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-syntax-logical-assignment-operators": {"version": "7.10.4", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-syntax-nullish-coalescing-operator": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-syntax-numeric-separator": {"version": "7.10.4", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-syntax-object-rest-spread": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-syntax-optional-catch-binding": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-syntax-optional-chaining": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-syntax-private-property-in-object": {"version": "7.14.5", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-syntax-top-level-await": {"version": "7.14.5", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-syntax-typescript": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-arrow-functions": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-async-generator-functions": {"version": "7.28.0", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-remap-async-to-generator": "^7.27.1", "@babel/traverse": "^7.28.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-async-to-generator": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-remap-async-to-generator": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-block-scoping": {"version": "7.28.4", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-class-properties": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-class-static-block": {"version": "7.28.3", "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.28.3", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.12.0"}}, "quran-app/node_modules/@babel/plugin-transform-classes": {"version": "7.28.4", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.3", "@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-globals": "^7.28.0", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-replace-supers": "^7.27.1", "@babel/traverse": "^7.28.4"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-computed-properties": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/template": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-destructuring": {"version": "7.28.0", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/traverse": "^7.28.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-export-namespace-from": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-flow-strip-types": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/plugin-syntax-flow": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-for-of": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-function-name": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-compilation-targets": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-literals": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-logical-assignment-operators": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-modules-commonjs": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-named-capturing-groups-regex": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "quran-app/node_modules/@babel/plugin-transform-nullish-coalescing-operator": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-numeric-separator": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-object-rest-spread": {"version": "7.28.4", "license": "MIT", "dependencies": {"@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-plugin-utils": "^7.27.1", "@babel/plugin-transform-destructuring": "^7.28.0", "@babel/plugin-transform-parameters": "^7.27.7", "@babel/traverse": "^7.28.4"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-optional-catch-binding": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-optional-chaining": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-parameters": {"version": "7.27.7", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-private-methods": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-private-property-in-object": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-react-display-name": {"version": "7.28.0", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-react-jsx": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-module-imports": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/plugin-syntax-jsx": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-react-jsx-development": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/plugin-transform-react-jsx": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-react-jsx-self": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-react-jsx-source": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-react-pure-annotations": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-regenerator": {"version": "7.28.4", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-runtime": {"version": "7.28.3", "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "babel-plugin-polyfill-corejs2": "^0.4.14", "babel-plugin-polyfill-corejs3": "^0.13.0", "babel-plugin-polyfill-regenerator": "^0.6.5", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-runtime/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "quran-app/node_modules/@babel/plugin-transform-shorthand-properties": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-spread": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-sticky-regex": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-typescript": {"version": "7.28.0", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.3", "@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1", "@babel/plugin-syntax-typescript": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/plugin-transform-unicode-regex": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/preset-react": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-validator-option": "^7.27.1", "@babel/plugin-transform-react-display-name": "^7.27.1", "@babel/plugin-transform-react-jsx": "^7.27.1", "@babel/plugin-transform-react-jsx-development": "^7.27.1", "@babel/plugin-transform-react-pure-annotations": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/preset-typescript": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-validator-option": "^7.27.1", "@babel/plugin-syntax-jsx": "^7.27.1", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/plugin-transform-typescript": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "quran-app/node_modules/@babel/runtime": {"version": "7.28.4", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "quran-app/node_modules/@babel/template": {"version": "7.27.2", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "quran-app/node_modules/@babel/template/node_modules/@babel/code-frame": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "engines": {"node": ">=6.9.0"}}, "quran-app/node_modules/@babel/traverse": {"version": "7.28.4", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.28.3", "@babel/helper-globals": "^7.28.0", "@babel/parser": "^7.28.4", "@babel/template": "^7.27.2", "@babel/types": "^7.28.4", "debug": "^4.3.1"}, "engines": {"node": ">=6.9.0"}}, "quran-app/node_modules/@babel/traverse--for-generate-function-map": {"name": "@babel/traverse", "version": "7.28.4", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.28.3", "@babel/helper-globals": "^7.28.0", "@babel/parser": "^7.28.4", "@babel/template": "^7.27.2", "@babel/types": "^7.28.4", "debug": "^4.3.1"}, "engines": {"node": ">=6.9.0"}}, "quran-app/node_modules/@babel/traverse--for-generate-function-map/node_modules/@babel/code-frame": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "engines": {"node": ">=6.9.0"}}, "quran-app/node_modules/@babel/traverse/node_modules/@babel/code-frame": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "engines": {"node": ">=6.9.0"}}, "quran-app/node_modules/@babel/types": {"version": "7.28.4", "license": "MIT", "dependencies": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "quran-app/node_modules/@expo/code-signing-certificates": {"version": "0.0.5", "license": "MIT", "dependencies": {"node-forge": "^1.2.1", "nullthrows": "^1.1.1"}}, "quran-app/node_modules/@expo/config": {"version": "12.0.10", "license": "MIT", "dependencies": {"@babel/code-frame": "~7.10.4", "@expo/config-plugins": "~54.0.2", "@expo/config-types": "^54.0.8", "@expo/json-file": "^10.0.7", "deepmerge": "^4.3.1", "getenv": "^2.0.0", "glob": "^10.4.2", "require-from-string": "^2.0.2", "resolve-from": "^5.0.0", "resolve-workspace-root": "^2.0.0", "semver": "^7.6.0", "slugify": "^1.3.4", "sucrase": "3.35.0"}}, "quran-app/node_modules/@expo/config-plugins": {"version": "54.0.2", "license": "MIT", "dependencies": {"@expo/config-types": "^54.0.8", "@expo/json-file": "~10.0.7", "@expo/plist": "^0.4.7", "@expo/sdk-runtime-versions": "^1.0.0", "chalk": "^4.1.2", "debug": "^4.3.5", "getenv": "^2.0.0", "glob": "^10.4.2", "resolve-from": "^5.0.0", "semver": "^7.5.4", "slash": "^3.0.0", "slugify": "^1.6.6", "xcode": "^3.0.1", "xml2js": "0.6.0"}}, "quran-app/node_modules/@expo/config-plugins/node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "quran-app/node_modules/@expo/config-plugins/node_modules/chalk": {"version": "4.1.2", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "quran-app/node_modules/@expo/config-plugins/node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "quran-app/node_modules/@expo/config-plugins/node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "quran-app/node_modules/@expo/config-plugins/node_modules/has-flag": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "quran-app/node_modules/@expo/config-plugins/node_modules/supports-color": {"version": "7.2.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/@expo/config-types": {"version": "54.0.8", "license": "MIT"}, "quran-app/node_modules/@expo/devcert": {"version": "1.2.0", "license": "MIT", "dependencies": {"@expo/sudo-prompt": "^9.3.1", "debug": "^3.1.0", "glob": "^10.4.2"}}, "quran-app/node_modules/@expo/devcert/node_modules/debug": {"version": "3.2.7", "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "quran-app/node_modules/@expo/devtools": {"version": "0.1.7", "license": "MIT", "dependencies": {"chalk": "^4.1.2"}, "peerDependencies": {"react": "*", "react-native": "*"}, "peerDependenciesMeta": {"react": {"optional": true}, "react-native": {"optional": true}}}, "quran-app/node_modules/@expo/devtools/node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "quran-app/node_modules/@expo/devtools/node_modules/chalk": {"version": "4.1.2", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "quran-app/node_modules/@expo/devtools/node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "quran-app/node_modules/@expo/devtools/node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "quran-app/node_modules/@expo/devtools/node_modules/has-flag": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "quran-app/node_modules/@expo/devtools/node_modules/supports-color": {"version": "7.2.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/@expo/env": {"version": "2.0.7", "license": "MIT", "dependencies": {"chalk": "^4.0.0", "debug": "^4.3.4", "dotenv": "~16.4.5", "dotenv-expand": "~11.0.6", "getenv": "^2.0.0"}}, "quran-app/node_modules/@expo/env/node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "quran-app/node_modules/@expo/env/node_modules/chalk": {"version": "4.1.2", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "quran-app/node_modules/@expo/env/node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "quran-app/node_modules/@expo/env/node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "quran-app/node_modules/@expo/env/node_modules/has-flag": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "quran-app/node_modules/@expo/env/node_modules/supports-color": {"version": "7.2.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/@expo/fingerprint": {"version": "0.15.1", "license": "MIT", "dependencies": {"@expo/spawn-async": "^1.7.2", "arg": "^5.0.2", "chalk": "^4.1.2", "debug": "^4.3.4", "getenv": "^2.0.0", "glob": "^10.4.2", "ignore": "^5.3.1", "minimatch": "^9.0.0", "p-limit": "^3.1.0", "resolve-from": "^5.0.0", "semver": "^7.6.0"}, "bin": {"fingerprint": "bin/cli.js"}}, "quran-app/node_modules/@expo/fingerprint/node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "quran-app/node_modules/@expo/fingerprint/node_modules/chalk": {"version": "4.1.2", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "quran-app/node_modules/@expo/fingerprint/node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "quran-app/node_modules/@expo/fingerprint/node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "quran-app/node_modules/@expo/fingerprint/node_modules/has-flag": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "quran-app/node_modules/@expo/fingerprint/node_modules/supports-color": {"version": "7.2.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/@expo/image-utils": {"version": "0.8.7", "license": "MIT", "dependencies": {"@expo/spawn-async": "^1.7.2", "chalk": "^4.0.0", "getenv": "^2.0.0", "jimp-compact": "0.16.1", "parse-png": "^2.1.0", "resolve-from": "^5.0.0", "resolve-global": "^1.0.0", "semver": "^7.6.0", "temp-dir": "~2.0.0", "unique-string": "~2.0.0"}}, "quran-app/node_modules/@expo/image-utils/node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "quran-app/node_modules/@expo/image-utils/node_modules/chalk": {"version": "4.1.2", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "quran-app/node_modules/@expo/image-utils/node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "quran-app/node_modules/@expo/image-utils/node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "quran-app/node_modules/@expo/image-utils/node_modules/has-flag": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "quran-app/node_modules/@expo/image-utils/node_modules/supports-color": {"version": "7.2.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/@expo/json-file": {"version": "10.0.7", "license": "MIT", "dependencies": {"@babel/code-frame": "~7.10.4", "json5": "^2.2.3"}}, "quran-app/node_modules/@expo/mcp-tunnel": {"version": "0.0.8", "license": "MIT", "dependencies": {"ws": "^8.18.3", "zod": "^3.25.76", "zod-to-json-schema": "^3.24.6"}, "peerDependencies": {"@modelcontextprotocol/sdk": "^1.13.2"}, "peerDependenciesMeta": {"@modelcontextprotocol/sdk": {"optional": true}}}, "quran-app/node_modules/@expo/mcp-tunnel/node_modules/ws": {"version": "8.18.3", "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "quran-app/node_modules/@expo/metro": {"version": "54.0.0", "license": "MIT", "dependencies": {"metro": "0.83.1", "metro-babel-transformer": "0.83.1", "metro-cache": "0.83.1", "metro-cache-key": "0.83.1", "metro-config": "0.83.1", "metro-core": "0.83.1", "metro-file-map": "0.83.1", "metro-resolver": "0.83.1", "metro-runtime": "0.83.1", "metro-source-map": "0.83.1", "metro-transform-plugins": "0.83.1", "metro-transform-worker": "0.83.1"}}, "quran-app/node_modules/@expo/osascript": {"version": "2.3.7", "license": "MIT", "dependencies": {"@expo/spawn-async": "^1.7.2", "exec-async": "^2.2.0"}, "engines": {"node": ">=12"}}, "quran-app/node_modules/@expo/package-manager": {"version": "1.9.8", "license": "MIT", "dependencies": {"@expo/json-file": "^10.0.7", "@expo/spawn-async": "^1.7.2", "chalk": "^4.0.0", "npm-package-arg": "^11.0.0", "ora": "^3.4.0", "resolve-workspace-root": "^2.0.0"}}, "quran-app/node_modules/@expo/package-manager/node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "quran-app/node_modules/@expo/package-manager/node_modules/chalk": {"version": "4.1.2", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "quran-app/node_modules/@expo/package-manager/node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "quran-app/node_modules/@expo/package-manager/node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "quran-app/node_modules/@expo/package-manager/node_modules/has-flag": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "quran-app/node_modules/@expo/package-manager/node_modules/supports-color": {"version": "7.2.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/@expo/plist": {"version": "0.4.7", "license": "MIT", "dependencies": {"@xmldom/xmldom": "^0.8.8", "base64-js": "^1.2.3", "xmlbuilder": "^15.1.1"}}, "quran-app/node_modules/@expo/schema-utils": {"version": "0.1.7", "license": "MIT"}, "quran-app/node_modules/@expo/sdk-runtime-versions": {"version": "1.0.0", "license": "MIT"}, "quran-app/node_modules/@expo/spawn-async": {"version": "1.7.2", "license": "MIT", "dependencies": {"cross-spawn": "^7.0.3"}, "engines": {"node": ">=12"}}, "quran-app/node_modules/@expo/sudo-prompt": {"version": "9.3.2", "license": "MIT"}, "quran-app/node_modules/@expo/ws-tunnel": {"version": "1.0.6", "license": "MIT"}, "quran-app/node_modules/@expo/xcpretty": {"version": "4.3.2", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@babel/code-frame": "7.10.4", "chalk": "^4.1.0", "find-up": "^5.0.0", "js-yaml": "^4.1.0"}, "bin": {"excpretty": "build/cli.js"}}, "quran-app/node_modules/@expo/xcpretty/node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "quran-app/node_modules/@expo/xcpretty/node_modules/argparse": {"version": "2.0.1", "license": "Python-2.0"}, "quran-app/node_modules/@expo/xcpretty/node_modules/chalk": {"version": "4.1.2", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "quran-app/node_modules/@expo/xcpretty/node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "quran-app/node_modules/@expo/xcpretty/node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "quran-app/node_modules/@expo/xcpretty/node_modules/find-up": {"version": "5.0.0", "license": "MIT", "dependencies": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "quran-app/node_modules/@expo/xcpretty/node_modules/has-flag": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "quran-app/node_modules/@expo/xcpretty/node_modules/js-yaml": {"version": "4.1.0", "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "quran-app/node_modules/@expo/xcpretty/node_modules/locate-path": {"version": "6.0.0", "license": "MIT", "dependencies": {"p-locate": "^5.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "quran-app/node_modules/@expo/xcpretty/node_modules/p-locate": {"version": "5.0.0", "license": "MIT", "dependencies": {"p-limit": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "quran-app/node_modules/@expo/xcpretty/node_modules/supports-color": {"version": "7.2.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/@isaacs/cliui": {"version": "8.0.2", "license": "ISC", "dependencies": {"string-width": "^5.1.2", "string-width-cjs": "npm:string-width@^4.2.0", "strip-ansi": "^7.0.1", "strip-ansi-cjs": "npm:strip-ansi@^6.0.1", "wrap-ansi": "^8.1.0", "wrap-ansi-cjs": "npm:wrap-ansi@^7.0.0"}, "engines": {"node": ">=12"}}, "quran-app/node_modules/@isaacs/fs-minipass": {"version": "4.0.1", "license": "ISC", "dependencies": {"minipass": "^7.0.4"}, "engines": {"node": ">=18.0.0"}}, "quran-app/node_modules/@isaacs/ttlcache": {"version": "1.4.1", "license": "ISC", "engines": {"node": ">=12"}}, "quran-app/node_modules/@istanbuljs/load-nyc-config": {"version": "1.1.0", "license": "ISC", "dependencies": {"camelcase": "^5.3.1", "find-up": "^4.1.0", "get-package-type": "^0.1.0", "js-yaml": "^3.13.1", "resolve-from": "^5.0.0"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/@istanbuljs/load-nyc-config/node_modules/camelcase": {"version": "5.3.1", "license": "MIT", "engines": {"node": ">=6"}}, "quran-app/node_modules/@istanbuljs/schema": {"version": "0.1.3", "license": "MIT", "engines": {"node": ">=8"}}, "quran-app/node_modules/@jest/create-cache-key-function": {"version": "29.7.0", "license": "MIT", "dependencies": {"@jest/types": "^29.6.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "quran-app/node_modules/@jest/environment": {"version": "29.7.0", "license": "MIT", "dependencies": {"@jest/fake-timers": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "jest-mock": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "quran-app/node_modules/@jest/fake-timers": {"version": "29.7.0", "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "@sinonjs/fake-timers": "^10.0.2", "@types/node": "*", "jest-message-util": "^29.7.0", "jest-mock": "^29.7.0", "jest-util": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "quran-app/node_modules/@jest/schemas": {"version": "29.6.3", "license": "MIT", "dependencies": {"@sinclair/typebox": "^0.27.8"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "quran-app/node_modules/@jest/transform": {"version": "29.7.0", "license": "MIT", "dependencies": {"@babel/core": "^7.11.6", "@jest/types": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.18", "babel-plugin-istanbul": "^6.1.1", "chalk": "^4.0.0", "convert-source-map": "^2.0.0", "fast-json-stable-stringify": "^2.1.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.7.0", "jest-regex-util": "^29.6.3", "jest-util": "^29.7.0", "micromatch": "^4.0.4", "pirates": "^4.0.4", "slash": "^3.0.0", "write-file-atomic": "^4.0.2"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "quran-app/node_modules/@jest/transform/node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "quran-app/node_modules/@jest/transform/node_modules/chalk": {"version": "4.1.2", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "quran-app/node_modules/@jest/transform/node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "quran-app/node_modules/@jest/transform/node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "quran-app/node_modules/@jest/transform/node_modules/has-flag": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "quran-app/node_modules/@jest/transform/node_modules/supports-color": {"version": "7.2.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/@jest/types": {"version": "29.6.3", "license": "MIT", "dependencies": {"@jest/schemas": "^29.6.3", "@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-reports": "^3.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "chalk": "^4.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "quran-app/node_modules/@jest/types/node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "quran-app/node_modules/@jest/types/node_modules/chalk": {"version": "4.1.2", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "quran-app/node_modules/@jest/types/node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "quran-app/node_modules/@jest/types/node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "quran-app/node_modules/@jest/types/node_modules/has-flag": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "quran-app/node_modules/@jest/types/node_modules/supports-color": {"version": "7.2.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/@jridgewell/gen-mapping": {"version": "0.3.13", "license": "MIT", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0", "@jridgewell/trace-mapping": "^0.3.24"}}, "quran-app/node_modules/@jridgewell/remapping": {"version": "2.3.5", "license": "MIT", "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.24"}}, "quran-app/node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "license": "MIT", "engines": {"node": ">=6.0.0"}}, "quran-app/node_modules/@jridgewell/source-map": {"version": "0.3.11", "license": "MIT", "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}}, "quran-app/node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.5", "license": "MIT"}, "quran-app/node_modules/@jridgewell/trace-mapping": {"version": "0.3.31", "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "quran-app/node_modules/@pkgjs/parseargs": {"version": "0.11.0", "license": "MIT", "optional": true, "engines": {"node": ">=14"}}, "quran-app/node_modules/@react-native/assets-registry": {"version": "0.81.4", "license": "MIT", "engines": {"node": ">= 20.19.4"}}, "quran-app/node_modules/@react-native/babel-plugin-codegen": {"version": "0.81.4", "license": "MIT", "dependencies": {"@babel/traverse": "^7.25.3", "@react-native/codegen": "0.81.4"}, "engines": {"node": ">= 20.19.4"}}, "quran-app/node_modules/@react-native/babel-preset": {"version": "0.81.4", "license": "MIT", "dependencies": {"@babel/core": "^7.25.2", "@babel/plugin-proposal-export-default-from": "^7.24.7", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-syntax-export-default-from": "^7.24.7", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/plugin-transform-arrow-functions": "^7.24.7", "@babel/plugin-transform-async-generator-functions": "^7.25.4", "@babel/plugin-transform-async-to-generator": "^7.24.7", "@babel/plugin-transform-block-scoping": "^7.25.0", "@babel/plugin-transform-class-properties": "^7.25.4", "@babel/plugin-transform-classes": "^7.25.4", "@babel/plugin-transform-computed-properties": "^7.24.7", "@babel/plugin-transform-destructuring": "^7.24.8", "@babel/plugin-transform-flow-strip-types": "^7.25.2", "@babel/plugin-transform-for-of": "^7.24.7", "@babel/plugin-transform-function-name": "^7.25.1", "@babel/plugin-transform-literals": "^7.25.2", "@babel/plugin-transform-logical-assignment-operators": "^7.24.7", "@babel/plugin-transform-modules-commonjs": "^7.24.8", "@babel/plugin-transform-named-capturing-groups-regex": "^7.24.7", "@babel/plugin-transform-nullish-coalescing-operator": "^7.24.7", "@babel/plugin-transform-numeric-separator": "^7.24.7", "@babel/plugin-transform-object-rest-spread": "^7.24.7", "@babel/plugin-transform-optional-catch-binding": "^7.24.7", "@babel/plugin-transform-optional-chaining": "^7.24.8", "@babel/plugin-transform-parameters": "^7.24.7", "@babel/plugin-transform-private-methods": "^7.24.7", "@babel/plugin-transform-private-property-in-object": "^7.24.7", "@babel/plugin-transform-react-display-name": "^7.24.7", "@babel/plugin-transform-react-jsx": "^7.25.2", "@babel/plugin-transform-react-jsx-self": "^7.24.7", "@babel/plugin-transform-react-jsx-source": "^7.24.7", "@babel/plugin-transform-regenerator": "^7.24.7", "@babel/plugin-transform-runtime": "^7.24.7", "@babel/plugin-transform-shorthand-properties": "^7.24.7", "@babel/plugin-transform-spread": "^7.24.7", "@babel/plugin-transform-sticky-regex": "^7.24.7", "@babel/plugin-transform-typescript": "^7.25.2", "@babel/plugin-transform-unicode-regex": "^7.24.7", "@babel/template": "^7.25.0", "@react-native/babel-plugin-codegen": "0.81.4", "babel-plugin-syntax-hermes-parser": "0.29.1", "babel-plugin-transform-flow-enums": "^0.0.2", "react-refresh": "^0.14.0"}, "engines": {"node": ">= 20.19.4"}, "peerDependencies": {"@babel/core": "*"}}, "quran-app/node_modules/@react-native/codegen": {"version": "0.81.4", "license": "MIT", "dependencies": {"@babel/core": "^7.25.2", "@babel/parser": "^7.25.3", "glob": "^7.1.1", "hermes-parser": "0.29.1", "invariant": "^2.2.4", "nullthrows": "^1.1.1", "yargs": "^17.6.2"}, "engines": {"node": ">= 20.19.4"}, "peerDependencies": {"@babel/core": "*"}}, "quran-app/node_modules/@react-native/codegen/node_modules/brace-expansion": {"version": "1.1.12", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "quran-app/node_modules/@react-native/codegen/node_modules/glob": {"version": "7.2.3", "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "quran-app/node_modules/@react-native/codegen/node_modules/minimatch": {"version": "3.1.2", "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "quran-app/node_modules/@react-native/community-cli-plugin": {"version": "0.81.4", "license": "MIT", "dependencies": {"@react-native/dev-middleware": "0.81.4", "debug": "^4.4.0", "invariant": "^2.2.4", "metro": "^0.83.1", "metro-config": "^0.83.1", "metro-core": "^0.83.1", "semver": "^7.1.3"}, "engines": {"node": ">= 20.19.4"}, "peerDependencies": {"@react-native-community/cli": "*", "@react-native/metro-config": "*"}, "peerDependenciesMeta": {"@react-native-community/cli": {"optional": true}, "@react-native/metro-config": {"optional": true}}}, "quran-app/node_modules/@react-native/debugger-frontend": {}, "quran-app/node_modules/@react-native/dev-middleware": {"version": "0.81.4", "license": "MIT", "dependencies": {"@isaacs/ttlcache": "^1.4.1", "@react-native/debugger-frontend": "0.81.4", "chrome-launcher": "^0.15.2", "chromium-edge-launcher": "^0.2.0", "connect": "^3.6.5", "debug": "^4.4.0", "invariant": "^2.2.4", "nullthrows": "^1.1.1", "open": "^7.0.3", "serve-static": "^1.16.2", "ws": "^6.2.3"}, "engines": {"node": ">= 20.19.4"}}, "quran-app/node_modules/@react-native/dev-middleware/node_modules/ws": {"version": "6.2.3", "license": "MIT", "dependencies": {"async-limiter": "~1.0.0"}}, "quran-app/node_modules/@react-native/gradle-plugin": {"version": "0.81.4", "license": "MIT", "engines": {"node": ">= 20.19.4"}}, "quran-app/node_modules/@react-native/js-polyfills": {"version": "0.81.4", "license": "MIT", "engines": {"node": ">= 20.19.4"}}, "quran-app/node_modules/@react-native/normalize-colors": {"version": "0.81.4", "license": "MIT"}, "quran-app/node_modules/@react-native/virtualized-lists": {"version": "0.81.4", "license": "MIT", "dependencies": {"invariant": "^2.2.4", "nullthrows": "^1.1.1"}, "engines": {"node": ">= 20.19.4"}, "peerDependencies": {"@types/react": "^19.1.0", "react": "*", "react-native": "*"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "quran-app/node_modules/@sinclair/typebox": {"version": "0.27.8", "license": "MIT"}, "quran-app/node_modules/@sinonjs/commons": {"version": "3.0.1", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"type-detect": "4.0.8"}}, "quran-app/node_modules/@sinonjs/fake-timers": {"version": "10.3.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@sinonjs/commons": "^3.0.0"}}, "quran-app/node_modules/@types/babel__core": {"version": "7.20.5", "license": "MIT", "dependencies": {"@babel/parser": "^7.20.7", "@babel/types": "^7.20.7", "@types/babel__generator": "*", "@types/babel__template": "*", "@types/babel__traverse": "*"}}, "quran-app/node_modules/@types/babel__generator": {"version": "7.27.0", "license": "MIT", "dependencies": {"@babel/types": "^7.0.0"}}, "quran-app/node_modules/@types/babel__template": {"version": "7.4.4", "license": "MIT", "dependencies": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0"}}, "quran-app/node_modules/@types/babel__traverse": {"version": "7.28.0", "license": "MIT", "dependencies": {"@babel/types": "^7.28.2"}}, "quran-app/node_modules/@types/graceful-fs": {"version": "4.1.9", "license": "MIT", "dependencies": {"@types/node": "*"}}, "quran-app/node_modules/@types/istanbul-lib-coverage": {"version": "2.0.6", "license": "MIT"}, "quran-app/node_modules/@types/istanbul-lib-report": {"version": "3.0.3", "license": "MIT", "dependencies": {"@types/istanbul-lib-coverage": "*"}}, "quran-app/node_modules/@types/istanbul-reports": {"version": "3.0.4", "license": "MIT", "dependencies": {"@types/istanbul-lib-report": "*"}}, "quran-app/node_modules/@types/node": {"version": "24.6.2", "license": "MIT", "dependencies": {"undici-types": "~7.13.0"}}, "quran-app/node_modules/@types/stack-utils": {"version": "2.0.3", "license": "MIT"}, "quran-app/node_modules/@types/yargs": {"version": "17.0.33", "license": "MIT", "dependencies": {"@types/yargs-parser": "*"}}, "quran-app/node_modules/@types/yargs-parser": {"version": "21.0.3", "license": "MIT"}, "quran-app/node_modules/@ungap/structured-clone": {"version": "1.3.0", "license": "ISC"}, "quran-app/node_modules/@urql/core": {"version": "5.2.0", "license": "MIT", "dependencies": {"@0no-co/graphql.web": "^1.0.13", "wonka": "^6.3.2"}}, "quran-app/node_modules/@urql/exchange-retry": {"version": "1.3.2", "license": "MIT", "dependencies": {"@urql/core": "^5.1.2", "wonka": "^6.3.2"}, "peerDependencies": {"@urql/core": "^5.0.0"}}, "quran-app/node_modules/@xmldom/xmldom": {"version": "0.8.11", "license": "MIT", "engines": {"node": ">=10.0.0"}}, "quran-app/node_modules/abort-controller": {"version": "3.0.0", "license": "MIT", "dependencies": {"event-target-shim": "^5.0.0"}, "engines": {"node": ">=6.5"}}, "quran-app/node_modules/accepts": {"version": "1.3.8", "license": "MIT", "dependencies": {"mime-types": "~2.1.34", "negotiator": "0.6.3"}, "engines": {"node": ">= 0.6"}}, "quran-app/node_modules/acorn": {"version": "8.15.0", "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "quran-app/node_modules/agent-base": {"version": "7.1.4", "license": "MIT", "engines": {"node": ">= 14"}}, "quran-app/node_modules/anser": {"version": "1.4.10", "license": "MIT"}, "quran-app/node_modules/ansi-escapes": {"version": "4.3.2", "license": "MIT", "dependencies": {"type-fest": "^0.21.3"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "quran-app/node_modules/ansi-escapes/node_modules/type-fest": {"version": "0.21.3", "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "quran-app/node_modules/ansi-regex": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=8"}}, "quran-app/node_modules/ansi-styles": {"version": "3.2.1", "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "quran-app/node_modules/any-promise": {"version": "1.3.0", "license": "MIT"}, "quran-app/node_modules/anymatch": {"version": "3.1.3", "license": "ISC", "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "engines": {"node": ">= 8"}}, "quran-app/node_modules/arg": {"version": "5.0.2", "license": "MIT"}, "quran-app/node_modules/argparse": {"version": "1.0.10", "license": "MIT", "dependencies": {"sprintf-js": "~1.0.2"}}, "quran-app/node_modules/asap": {"version": "2.0.6", "license": "MIT"}, "quran-app/node_modules/async-limiter": {"version": "1.0.1", "license": "MIT"}, "quran-app/node_modules/babel-jest": {"version": "29.7.0", "license": "MIT", "dependencies": {"@jest/transform": "^29.7.0", "@types/babel__core": "^7.1.14", "babel-plugin-istanbul": "^6.1.1", "babel-preset-jest": "^29.6.3", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "slash": "^3.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"@babel/core": "^7.8.0"}}, "quran-app/node_modules/babel-jest/node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "quran-app/node_modules/babel-jest/node_modules/chalk": {"version": "4.1.2", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "quran-app/node_modules/babel-jest/node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "quran-app/node_modules/babel-jest/node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "quran-app/node_modules/babel-jest/node_modules/has-flag": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "quran-app/node_modules/babel-jest/node_modules/supports-color": {"version": "7.2.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/babel-plugin-istanbul": {"version": "6.1.1", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@istanbuljs/load-nyc-config": "^1.0.0", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-instrument": "^5.0.4", "test-exclude": "^6.0.0"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/babel-plugin-jest-hoist": {"version": "29.6.3", "license": "MIT", "dependencies": {"@babel/template": "^7.3.3", "@babel/types": "^7.3.3", "@types/babel__core": "^7.1.14", "@types/babel__traverse": "^7.0.6"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "quran-app/node_modules/babel-plugin-polyfill-corejs2": {"version": "0.4.14", "license": "MIT", "dependencies": {"@babel/compat-data": "^7.27.7", "@babel/helper-define-polyfill-provider": "^0.6.5", "semver": "^6.3.1"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}}, "quran-app/node_modules/babel-plugin-polyfill-corejs2/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "quran-app/node_modules/babel-plugin-polyfill-corejs3": {"version": "0.13.0", "license": "MIT", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.6.5", "core-js-compat": "^3.43.0"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}}, "quran-app/node_modules/babel-plugin-polyfill-regenerator": {"version": "0.6.5", "license": "MIT", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.6.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}}, "quran-app/node_modules/babel-plugin-react-compiler": {"version": "19.1.0-rc.3", "license": "MIT", "dependencies": {"@babel/types": "^7.26.0"}}, "quran-app/node_modules/babel-plugin-react-native-web": {"version": "0.21.1", "license": "MIT"}, "quran-app/node_modules/babel-plugin-syntax-hermes-parser": {"version": "0.29.1", "license": "MIT", "dependencies": {"hermes-parser": "0.29.1"}}, "quran-app/node_modules/babel-plugin-transform-flow-enums": {"version": "0.0.2", "license": "MIT", "dependencies": {"@babel/plugin-syntax-flow": "^7.12.1"}}, "quran-app/node_modules/babel-preset-current-node-syntax": {"version": "1.2.0", "license": "MIT", "dependencies": {"@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/plugin-syntax-bigint": "^7.8.3", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-class-static-block": "^7.14.5", "@babel/plugin-syntax-import-attributes": "^7.24.7", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/plugin-syntax-json-strings": "^7.8.3", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-syntax-numeric-separator": "^7.10.4", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/plugin-syntax-private-property-in-object": "^7.14.5", "@babel/plugin-syntax-top-level-await": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0 || ^8.0.0-0"}}, "quran-app/node_modules/babel-preset-jest": {"version": "29.6.3", "license": "MIT", "dependencies": {"babel-plugin-jest-hoist": "^29.6.3", "babel-preset-current-node-syntax": "^1.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "quran-app/node_modules/balanced-match": {"version": "1.0.2", "license": "MIT"}, "quran-app/node_modules/base64-js": {"version": "1.5.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "quran-app/node_modules/baseline-browser-mapping": {"version": "2.8.12", "license": "Apache-2.0", "bin": {"baseline-browser-mapping": "dist/cli.js"}}, "quran-app/node_modules/better-opn": {"version": "3.0.2", "license": "MIT", "dependencies": {"open": "^8.0.4"}, "engines": {"node": ">=12.0.0"}}, "quran-app/node_modules/better-opn/node_modules/open": {"version": "8.4.2", "license": "MIT", "dependencies": {"define-lazy-prop": "^2.0.0", "is-docker": "^2.1.1", "is-wsl": "^2.2.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "quran-app/node_modules/big-integer": {"version": "1.6.52", "license": "Unlicense", "engines": {"node": ">=0.6"}}, "quran-app/node_modules/bplist-creator": {"version": "0.1.0", "license": "MIT", "dependencies": {"stream-buffers": "2.2.x"}}, "quran-app/node_modules/bplist-parser": {"version": "0.3.1", "license": "MIT", "dependencies": {"big-integer": "1.6.x"}, "engines": {"node": ">= 5.10.0"}}, "quran-app/node_modules/brace-expansion": {"version": "2.0.2", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "quran-app/node_modules/braces": {"version": "3.0.3", "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/browserslist": {"version": "4.26.3", "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"baseline-browser-mapping": "^2.8.9", "caniuse-lite": "^1.0.30001746", "electron-to-chromium": "^1.5.227", "node-releases": "^2.0.21", "update-browserslist-db": "^1.1.3"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "quran-app/node_modules/browserslist/node_modules/caniuse-lite": {"version": "1.0.30001748", "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "CC-BY-4.0"}, "quran-app/node_modules/bser": {"version": "2.1.1", "license": "Apache-2.0", "dependencies": {"node-int64": "^0.4.0"}}, "quran-app/node_modules/buffer": {"version": "5.7.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}}, "quran-app/node_modules/buffer-from": {"version": "1.1.2", "license": "MIT"}, "quran-app/node_modules/bytes": {"version": "3.1.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "quran-app/node_modules/caller-callsite": {"version": "2.0.0", "license": "MIT", "dependencies": {"callsites": "^2.0.0"}, "engines": {"node": ">=4"}}, "quran-app/node_modules/caller-path": {"version": "2.0.0", "license": "MIT", "dependencies": {"caller-callsite": "^2.0.0"}, "engines": {"node": ">=4"}}, "quran-app/node_modules/callsites": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "quran-app/node_modules/camelcase": {"version": "6.3.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "quran-app/node_modules/chalk": {"version": "2.4.2", "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "quran-app/node_modules/chownr": {"version": "3.0.0", "license": "BlueOak-1.0.0", "engines": {"node": ">=18"}}, "quran-app/node_modules/chrome-launcher": {"version": "0.15.2", "license": "Apache-2.0", "dependencies": {"@types/node": "*", "escape-string-regexp": "^4.0.0", "is-wsl": "^2.2.0", "lighthouse-logger": "^1.0.0"}, "bin": {"print-chrome-path": "bin/print-chrome-path.js"}, "engines": {"node": ">=12.13.0"}}, "quran-app/node_modules/chrome-launcher/node_modules/escape-string-regexp": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "quran-app/node_modules/chromium-edge-launcher": {"version": "0.2.0", "license": "Apache-2.0", "dependencies": {"@types/node": "*", "escape-string-regexp": "^4.0.0", "is-wsl": "^2.2.0", "lighthouse-logger": "^1.0.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2"}}, "quran-app/node_modules/chromium-edge-launcher/node_modules/escape-string-regexp": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "quran-app/node_modules/ci-info": {"version": "2.0.0", "license": "MIT"}, "quran-app/node_modules/cli-cursor": {"version": "2.1.0", "license": "MIT", "dependencies": {"restore-cursor": "^2.0.0"}, "engines": {"node": ">=4"}}, "quran-app/node_modules/cli-spinners": {"version": "2.9.2", "license": "MIT", "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "quran-app/node_modules/cliui": {"version": "8.0.1", "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=12"}}, "quran-app/node_modules/cliui/node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "quran-app/node_modules/cliui/node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "quran-app/node_modules/cliui/node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "quran-app/node_modules/cliui/node_modules/emoji-regex": {"version": "8.0.0", "license": "MIT"}, "quran-app/node_modules/cliui/node_modules/string-width": {"version": "4.2.3", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/cliui/node_modules/strip-ansi": {"version": "6.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/cliui/node_modules/wrap-ansi": {"version": "7.0.0", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "quran-app/node_modules/clone": {"version": "1.0.4", "license": "MIT", "engines": {"node": ">=0.8"}}, "quran-app/node_modules/color-convert": {"version": "1.9.3", "license": "MIT", "dependencies": {"color-name": "1.1.3"}}, "quran-app/node_modules/color-name": {"version": "1.1.3", "license": "MIT"}, "quran-app/node_modules/commander": {"version": "7.2.0", "license": "MIT", "engines": {"node": ">= 10"}}, "quran-app/node_modules/compressible": {"version": "2.0.18", "license": "MIT", "dependencies": {"mime-db": ">= 1.43.0 < 2"}, "engines": {"node": ">= 0.6"}}, "quran-app/node_modules/compression": {"version": "1.8.1", "license": "MIT", "dependencies": {"bytes": "3.1.2", "compressible": "~2.0.18", "debug": "2.6.9", "negotiator": "~0.6.4", "on-headers": "~1.1.0", "safe-buffer": "5.2.1", "vary": "~1.1.2"}, "engines": {"node": ">= 0.8.0"}}, "quran-app/node_modules/compression/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "quran-app/node_modules/compression/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "quran-app/node_modules/compression/node_modules/negotiator": {"version": "0.6.4", "license": "MIT", "engines": {"node": ">= 0.6"}}, "quran-app/node_modules/concat-map": {"version": "0.0.1", "license": "MIT"}, "quran-app/node_modules/connect": {"version": "3.7.0", "license": "MIT", "dependencies": {"debug": "2.6.9", "finalhandler": "1.1.2", "parseurl": "~1.3.3", "utils-merge": "1.0.1"}, "engines": {"node": ">= 0.10.0"}}, "quran-app/node_modules/connect/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "quran-app/node_modules/connect/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "quran-app/node_modules/convert-source-map": {"version": "2.0.0", "license": "MIT"}, "quran-app/node_modules/core-js-compat": {"version": "3.45.1", "license": "MIT", "dependencies": {"browserslist": "^4.25.3"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/core-js"}}, "quran-app/node_modules/cosmiconfig": {"version": "5.2.1", "license": "MIT", "dependencies": {"import-fresh": "^2.0.0", "is-directory": "^0.3.1", "js-yaml": "^3.13.1", "parse-json": "^4.0.0"}, "engines": {"node": ">=4"}}, "quran-app/node_modules/cross-spawn": {"version": "7.0.6", "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "quran-app/node_modules/crypto-random-string": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "quran-app/node_modules/debug": {"version": "4.4.3", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "quran-app/node_modules/deep-extend": {"version": "0.6.0", "license": "MIT", "engines": {"node": ">=4.0.0"}}, "quran-app/node_modules/deepmerge": {"version": "4.3.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "quran-app/node_modules/defaults": {"version": "1.0.4", "license": "MIT", "dependencies": {"clone": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "quran-app/node_modules/define-lazy-prop": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "quran-app/node_modules/depd": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "quran-app/node_modules/destroy": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "quran-app/node_modules/detect-libc": {"version": "2.1.2", "license": "Apache-2.0", "engines": {"node": ">=8"}}, "quran-app/node_modules/dotenv": {"version": "16.4.7", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}, "funding": {"url": "https://dotenvx.com"}}, "quran-app/node_modules/dotenv-expand": {"version": "11.0.7", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"dotenv": "^16.4.5"}, "engines": {"node": ">=12"}, "funding": {"url": "https://dotenvx.com"}}, "quran-app/node_modules/eastasianwidth": {"version": "0.2.0", "license": "MIT"}, "quran-app/node_modules/ee-first": {"version": "1.1.1", "license": "MIT"}, "quran-app/node_modules/electron-to-chromium": {"version": "1.5.230", "license": "ISC"}, "quran-app/node_modules/emoji-regex": {"version": "9.2.2", "license": "MIT"}, "quran-app/node_modules/encodeurl": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "quran-app/node_modules/env-editor": {"version": "0.4.2", "license": "MIT", "engines": {"node": ">=8"}}, "quran-app/node_modules/error-ex": {"version": "1.3.4", "license": "MIT", "dependencies": {"is-arrayish": "^0.2.1"}}, "quran-app/node_modules/error-stack-parser": {"version": "2.1.4", "license": "MIT", "dependencies": {"stackframe": "^1.3.4"}}, "quran-app/node_modules/escalade": {"version": "3.2.0", "license": "MIT", "engines": {"node": ">=6"}}, "quran-app/node_modules/escape-html": {"version": "1.0.3", "license": "MIT"}, "quran-app/node_modules/escape-string-regexp": {"version": "1.0.5", "license": "MIT", "engines": {"node": ">=0.8.0"}}, "quran-app/node_modules/esprima": {"version": "4.0.1", "license": "BSD-2-<PERSON><PERSON>", "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=4"}}, "quran-app/node_modules/etag": {"version": "1.8.1", "license": "MIT", "engines": {"node": ">= 0.6"}}, "quran-app/node_modules/event-target-shim": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=6"}}, "quran-app/node_modules/exec-async": {"version": "2.2.0", "license": "MIT"}, "quran-app/node_modules/expo": {"version": "54.0.12", "license": "MIT", "dependencies": {"@babel/runtime": "^7.20.0", "@expo/cli": "54.0.10", "@expo/config": "~12.0.10", "@expo/config-plugins": "~54.0.2", "@expo/devtools": "0.1.7", "@expo/fingerprint": "0.15.1", "@expo/metro": "~54.0.0", "@expo/metro-config": "54.0.6", "@expo/vector-icons": "^15.0.2", "@ungap/structured-clone": "^1.3.0", "babel-preset-expo": "~54.0.3", "expo-asset": "~12.0.9", "expo-constants": "~18.0.9", "expo-file-system": "~19.0.16", "expo-font": "~14.0.8", "expo-keep-awake": "~15.0.7", "expo-modules-autolinking": "3.0.14", "expo-modules-core": "3.0.20", "pretty-format": "^29.7.0", "react-refresh": "^0.14.2", "whatwg-url-without-unicode": "8.0.0-3"}, "bin": {"expo": "bin/cli", "expo-modules-autolinking": "bin/autolinking", "fingerprint": "bin/fingerprint"}, "peerDependencies": {"@expo/dom-webview": "*", "@expo/metro-runtime": "*", "react": "*", "react-native": "*", "react-native-webview": "*"}, "peerDependenciesMeta": {"@expo/dom-webview": {"optional": true}, "@expo/metro-runtime": {"optional": true}, "react-native-webview": {"optional": true}}}, "quran-app/node_modules/expo-av": {"version": "16.0.7", "license": "MIT", "peerDependencies": {"expo": "*", "react": "*", "react-native": "*", "react-native-web": "*"}, "peerDependenciesMeta": {"react-native-web": {"optional": true}}}, "quran-app/node_modules/expo-modules-autolinking": {"version": "3.0.14", "license": "MIT", "dependencies": {"@expo/spawn-async": "^1.7.2", "chalk": "^4.1.0", "commander": "^7.2.0", "glob": "^10.4.2", "require-from-string": "^2.0.2", "resolve-from": "^5.0.0"}, "bin": {"expo-modules-autolinking": "bin/expo-modules-autolinking.js"}}, "quran-app/node_modules/expo-modules-autolinking/node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "quran-app/node_modules/expo-modules-autolinking/node_modules/chalk": {"version": "4.1.2", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "quran-app/node_modules/expo-modules-autolinking/node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "quran-app/node_modules/expo-modules-autolinking/node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "quran-app/node_modules/expo-modules-autolinking/node_modules/has-flag": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "quran-app/node_modules/expo-modules-autolinking/node_modules/supports-color": {"version": "7.2.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/expo-modules-core": {"version": "3.0.20", "license": "MIT", "dependencies": {"invariant": "^2.2.4"}, "peerDependencies": {"react": "*", "react-native": "*"}}, "quran-app/node_modules/expo-server": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=20.16.0"}}, "quran-app/node_modules/expo-status-bar": {"version": "3.0.8", "license": "MIT", "dependencies": {"react-native-is-edge-to-edge": "^1.2.1"}, "peerDependencies": {"react": "*", "react-native": "*"}}, "quran-app/node_modules/expo/node_modules/@babel/code-frame": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "engines": {"node": ">=6.9.0"}}, "quran-app/node_modules/expo/node_modules/@expo/cli": {"version": "54.0.10", "license": "MIT", "dependencies": {"@0no-co/graphql.web": "^1.0.8", "@expo/code-signing-certificates": "^0.0.5", "@expo/config": "~12.0.10", "@expo/config-plugins": "~54.0.2", "@expo/devcert": "^1.1.2", "@expo/env": "~2.0.7", "@expo/image-utils": "^0.8.7", "@expo/json-file": "^10.0.7", "@expo/mcp-tunnel": "~0.0.7", "@expo/metro": "~54.0.0", "@expo/metro-config": "~54.0.6", "@expo/osascript": "^2.3.7", "@expo/package-manager": "^1.9.8", "@expo/plist": "^0.4.7", "@expo/prebuild-config": "^54.0.4", "@expo/schema-utils": "^0.1.7", "@expo/spawn-async": "^1.7.2", "@expo/ws-tunnel": "^1.0.1", "@expo/xcpretty": "^4.3.0", "@react-native/dev-middleware": "0.81.4", "@urql/core": "^5.0.6", "@urql/exchange-retry": "^1.3.0", "accepts": "^1.3.8", "arg": "^5.0.2", "better-opn": "~3.0.2", "bplist-creator": "0.1.0", "bplist-parser": "^0.3.1", "chalk": "^4.0.0", "ci-info": "^3.3.0", "compression": "^1.7.4", "connect": "^3.7.0", "debug": "^4.3.4", "env-editor": "^0.4.1", "expo-server": "^1.0.0", "freeport-async": "^2.0.0", "getenv": "^2.0.0", "glob": "^10.4.2", "lan-network": "^0.1.6", "minimatch": "^9.0.0", "node-forge": "^1.3.1", "npm-package-arg": "^11.0.0", "ora": "^3.4.0", "picomatch": "^3.0.1", "pretty-bytes": "^5.6.0", "pretty-format": "^29.7.0", "progress": "^2.0.3", "prompts": "^2.3.2", "qrcode-terminal": "0.11.0", "require-from-string": "^2.0.2", "requireg": "^0.2.2", "resolve": "^1.22.2", "resolve-from": "^5.0.0", "resolve.exports": "^2.0.3", "semver": "^7.6.0", "send": "^0.19.0", "slugify": "^1.3.4", "source-map-support": "~0.5.21", "stacktrace-parser": "^0.1.10", "structured-headers": "^0.4.1", "tar": "^7.4.3", "terminal-link": "^2.1.1", "undici": "^6.18.2", "wrap-ansi": "^7.0.0", "ws": "^8.12.1"}, "bin": {"expo-internal": "build/bin/cli"}, "peerDependencies": {"expo": "*", "expo-router": "*", "react-native": "*"}, "peerDependenciesMeta": {"expo-router": {"optional": true}, "react-native": {"optional": true}}}, "quran-app/node_modules/expo/node_modules/@expo/cli/node_modules/@expo/prebuild-config": {"version": "54.0.4", "license": "MIT", "dependencies": {"@expo/config": "~12.0.9", "@expo/config-plugins": "~54.0.2", "@expo/config-types": "^54.0.8", "@expo/image-utils": "^0.8.7", "@expo/json-file": "^10.0.7", "@react-native/normalize-colors": "0.81.4", "debug": "^4.3.1", "resolve-from": "^5.0.0", "semver": "^7.6.0", "xml2js": "0.6.0"}, "peerDependencies": {"expo": "*"}}, "quran-app/node_modules/expo/node_modules/@expo/metro-config": {"version": "54.0.6", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.20.0", "@babel/core": "^7.20.0", "@babel/generator": "^7.20.5", "@expo/config": "~12.0.10", "@expo/env": "~2.0.7", "@expo/json-file": "~10.0.7", "@expo/metro": "~54.0.0", "@expo/spawn-async": "^1.7.2", "browserslist": "^4.25.0", "chalk": "^4.1.0", "debug": "^4.3.2", "dotenv": "~16.4.5", "dotenv-expand": "~11.0.6", "getenv": "^2.0.0", "glob": "^10.4.2", "hermes-parser": "^0.29.1", "jsc-safe-url": "^0.2.4", "lightningcss": "^1.30.1", "minimatch": "^9.0.0", "postcss": "~8.4.32", "resolve-from": "^5.0.0"}, "peerDependencies": {"expo": "*"}, "peerDependenciesMeta": {"expo": {"optional": true}}}, "quran-app/node_modules/expo/node_modules/@expo/vector-icons": {"version": "15.0.2", "license": "MIT", "peerDependencies": {"expo-font": ">=14.0.4", "react": "*", "react-native": "*"}}, "quran-app/node_modules/expo/node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "quran-app/node_modules/expo/node_modules/babel-preset-expo": {"version": "54.0.3", "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.25.9", "@babel/plugin-proposal-decorators": "^7.12.9", "@babel/plugin-proposal-export-default-from": "^7.24.7", "@babel/plugin-syntax-export-default-from": "^7.24.7", "@babel/plugin-transform-class-static-block": "^7.27.1", "@babel/plugin-transform-export-namespace-from": "^7.25.9", "@babel/plugin-transform-flow-strip-types": "^7.25.2", "@babel/plugin-transform-modules-commonjs": "^7.24.8", "@babel/plugin-transform-object-rest-spread": "^7.24.7", "@babel/plugin-transform-parameters": "^7.24.7", "@babel/plugin-transform-private-methods": "^7.24.7", "@babel/plugin-transform-private-property-in-object": "^7.24.7", "@babel/plugin-transform-runtime": "^7.24.7", "@babel/preset-react": "^7.22.15", "@babel/preset-typescript": "^7.23.0", "@react-native/babel-preset": "0.81.4", "babel-plugin-react-compiler": "^19.1.0-rc.2", "babel-plugin-react-native-web": "~0.21.0", "babel-plugin-syntax-hermes-parser": "^0.29.1", "babel-plugin-transform-flow-enums": "^0.0.2", "debug": "^4.3.4", "resolve-from": "^5.0.0"}, "peerDependencies": {"@babel/runtime": "^7.20.0", "expo": "*", "react-refresh": ">=0.14.0 <1.0.0"}, "peerDependenciesMeta": {"@babel/runtime": {"optional": true}, "expo": {"optional": true}}}, "quran-app/node_modules/expo/node_modules/chalk": {"version": "4.1.2", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "quran-app/node_modules/expo/node_modules/ci-info": {"version": "3.9.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/sibir<PERSON>-s"}], "license": "MIT", "engines": {"node": ">=8"}}, "quran-app/node_modules/expo/node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "quran-app/node_modules/expo/node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "quran-app/node_modules/expo/node_modules/emoji-regex": {"version": "8.0.0", "license": "MIT"}, "quran-app/node_modules/expo/node_modules/expo-asset": {"version": "12.0.9", "license": "MIT", "dependencies": {"@expo/image-utils": "^0.8.7", "expo-constants": "~18.0.9"}, "peerDependencies": {"expo": "*", "react": "*", "react-native": "*"}}, "quran-app/node_modules/expo/node_modules/expo-constants": {"version": "18.0.9", "license": "MIT", "dependencies": {"@expo/config": "~12.0.9", "@expo/env": "~2.0.7"}, "peerDependencies": {"expo": "*", "react-native": "*"}}, "quran-app/node_modules/expo/node_modules/expo-file-system": {"version": "19.0.16", "license": "MIT", "peerDependencies": {"expo": "*", "react-native": "*"}}, "quran-app/node_modules/expo/node_modules/expo-font": {"version": "14.0.8", "license": "MIT", "dependencies": {"fontfaceobserver": "^2.1.0"}, "peerDependencies": {"expo": "*", "react": "*", "react-native": "*"}}, "quran-app/node_modules/expo/node_modules/expo-keep-awake": {"version": "15.0.7", "license": "MIT", "peerDependencies": {"expo": "*", "react": "*"}}, "quran-app/node_modules/expo/node_modules/has-flag": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "quran-app/node_modules/expo/node_modules/picomatch": {"version": "3.0.1", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "quran-app/node_modules/expo/node_modules/string-width": {"version": "4.2.3", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/expo/node_modules/strip-ansi": {"version": "6.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/expo/node_modules/supports-color": {"version": "7.2.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/expo/node_modules/wrap-ansi": {"version": "7.0.0", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "quran-app/node_modules/expo/node_modules/ws": {"version": "8.18.3", "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "quran-app/node_modules/exponential-backoff": {"version": "3.1.2", "license": "Apache-2.0"}, "quran-app/node_modules/fast-json-stable-stringify": {"version": "2.1.0", "license": "MIT"}, "quran-app/node_modules/fb-watchman": {"version": "2.0.2", "license": "Apache-2.0", "dependencies": {"bser": "2.1.1"}}, "quran-app/node_modules/fill-range": {"version": "7.1.1", "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/finalhandler": {"version": "1.1.2", "license": "MIT", "dependencies": {"debug": "2.6.9", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "parseurl": "~1.3.3", "statuses": "~1.5.0", "unpipe": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "quran-app/node_modules/finalhandler/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "quran-app/node_modules/finalhandler/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "quran-app/node_modules/find-up": {"version": "4.1.0", "license": "MIT", "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/flow-enums-runtime": {"version": "0.0.6", "license": "MIT"}, "quran-app/node_modules/fontfaceobserver": {"version": "2.3.0", "license": "BSD-2-<PERSON><PERSON>"}, "quran-app/node_modules/foreground-child": {"version": "3.3.1", "license": "ISC", "dependencies": {"cross-spawn": "^7.0.6", "signal-exit": "^4.0.1"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "quran-app/node_modules/freeport-async": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "quran-app/node_modules/fresh": {"version": "0.5.2", "license": "MIT", "engines": {"node": ">= 0.6"}}, "quran-app/node_modules/fs.realpath": {"version": "1.0.0", "license": "ISC"}, "quran-app/node_modules/fsevents": {"optional": true}, "quran-app/node_modules/function-bind": {"version": "1.1.2", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "quran-app/node_modules/gensync": {"version": "1.0.0-beta.2", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "quran-app/node_modules/get-caller-file": {"version": "2.0.5", "license": "ISC", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "quran-app/node_modules/get-package-type": {"version": "0.1.0", "license": "MIT", "engines": {"node": ">=8.0.0"}}, "quran-app/node_modules/getenv": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=6"}}, "quran-app/node_modules/glob": {"version": "10.4.5", "license": "ISC", "dependencies": {"foreground-child": "^3.1.0", "jackspeak": "^3.1.2", "minimatch": "^9.0.4", "minipass": "^7.1.2", "package-json-from-dist": "^1.0.0", "path-scurry": "^1.11.1"}, "bin": {"glob": "dist/esm/bin.mjs"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "quran-app/node_modules/global-dirs": {"version": "0.1.1", "license": "MIT", "dependencies": {"ini": "^1.3.4"}, "engines": {"node": ">=4"}}, "quran-app/node_modules/graceful-fs": {"version": "4.2.11", "license": "ISC"}, "quran-app/node_modules/has-flag": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "quran-app/node_modules/hasown": {"version": "2.0.2", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "quran-app/node_modules/hermes-estree": {"version": "0.29.1", "license": "MIT"}, "quran-app/node_modules/hermes-parser": {"version": "0.29.1", "license": "MIT", "dependencies": {"hermes-estree": "0.29.1"}}, "quran-app/node_modules/hosted-git-info": {"version": "7.0.2", "license": "ISC", "dependencies": {"lru-cache": "^10.0.1"}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "quran-app/node_modules/hosted-git-info/node_modules/lru-cache": {"version": "10.4.3", "license": "ISC"}, "quran-app/node_modules/http-errors": {"version": "2.0.0", "license": "MIT", "dependencies": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}, "engines": {"node": ">= 0.8"}}, "quran-app/node_modules/http-errors/node_modules/statuses": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">= 0.8"}}, "quran-app/node_modules/https-proxy-agent": {"version": "7.0.6", "license": "MIT", "dependencies": {"agent-base": "^7.1.2", "debug": "4"}, "engines": {"node": ">= 14"}}, "quran-app/node_modules/ieee754": {"version": "1.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "BSD-3-<PERSON><PERSON>"}, "quran-app/node_modules/ignore": {"version": "5.3.2", "license": "MIT", "engines": {"node": ">= 4"}}, "quran-app/node_modules/image-size": {"version": "1.2.1", "license": "MIT", "dependencies": {"queue": "6.0.2"}, "bin": {"image-size": "bin/image-size.js"}, "engines": {"node": ">=16.x"}}, "quran-app/node_modules/import-fresh": {"version": "2.0.0", "license": "MIT", "dependencies": {"caller-path": "^2.0.0", "resolve-from": "^3.0.0"}, "engines": {"node": ">=4"}}, "quran-app/node_modules/import-fresh/node_modules/resolve-from": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "quran-app/node_modules/imurmurhash": {"version": "0.1.4", "license": "MIT", "engines": {"node": ">=0.8.19"}}, "quran-app/node_modules/inflight": {"version": "1.0.6", "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "quran-app/node_modules/inherits": {"version": "2.0.4", "license": "ISC"}, "quran-app/node_modules/ini": {"version": "1.3.8", "license": "ISC"}, "quran-app/node_modules/invariant": {"version": "2.2.4", "license": "MIT", "dependencies": {"loose-envify": "^1.0.0"}}, "quran-app/node_modules/is-arrayish": {"version": "0.2.1", "license": "MIT"}, "quran-app/node_modules/is-core-module": {"version": "2.16.1", "license": "MIT", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "quran-app/node_modules/is-directory": {"version": "0.3.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "quran-app/node_modules/is-docker": {"version": "2.2.1", "license": "MIT", "bin": {"is-docker": "cli.js"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "quran-app/node_modules/is-fullwidth-code-point": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "quran-app/node_modules/is-number": {"version": "7.0.0", "license": "MIT", "engines": {"node": ">=0.12.0"}}, "quran-app/node_modules/is-wsl": {"version": "2.2.0", "license": "MIT", "dependencies": {"is-docker": "^2.0.0"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/isexe": {"version": "2.0.0", "license": "ISC"}, "quran-app/node_modules/istanbul-lib-coverage": {"version": "3.2.2", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=8"}}, "quran-app/node_modules/istanbul-lib-instrument": {"version": "5.2.1", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@babel/core": "^7.12.3", "@babel/parser": "^7.14.7", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-coverage": "^3.2.0", "semver": "^6.3.0"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/istanbul-lib-instrument/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "quran-app/node_modules/jackspeak": {"version": "3.4.3", "license": "BlueOak-1.0.0", "dependencies": {"@isaacs/cliui": "^8.0.2"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "optionalDependencies": {"@pkgjs/parseargs": "^0.11.0"}}, "quran-app/node_modules/jest-environment-node": {"version": "29.7.0", "license": "MIT", "dependencies": {"@jest/environment": "^29.7.0", "@jest/fake-timers": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "jest-mock": "^29.7.0", "jest-util": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "quran-app/node_modules/jest-get-type": {"version": "29.6.3", "license": "MIT", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "quran-app/node_modules/jest-haste-map": {"version": "29.7.0", "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "@types/graceful-fs": "^4.1.3", "@types/node": "*", "anymatch": "^3.0.3", "fb-watchman": "^2.0.0", "graceful-fs": "^4.2.9", "jest-regex-util": "^29.6.3", "jest-util": "^29.7.0", "jest-worker": "^29.7.0", "micromatch": "^4.0.4", "walker": "^1.0.8"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "optionalDependencies": {"fsevents": "^2.3.2"}}, "quran-app/node_modules/jest-message-util": {"version": "29.7.0", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.12.13", "@jest/types": "^29.6.3", "@types/stack-utils": "^2.0.0", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "micromatch": "^4.0.4", "pretty-format": "^29.7.0", "slash": "^3.0.0", "stack-utils": "^2.0.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "quran-app/node_modules/jest-message-util/node_modules/@babel/code-frame": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "engines": {"node": ">=6.9.0"}}, "quran-app/node_modules/jest-message-util/node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "quran-app/node_modules/jest-message-util/node_modules/chalk": {"version": "4.1.2", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "quran-app/node_modules/jest-message-util/node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "quran-app/node_modules/jest-message-util/node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "quran-app/node_modules/jest-message-util/node_modules/has-flag": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "quran-app/node_modules/jest-message-util/node_modules/supports-color": {"version": "7.2.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/jest-mock": {"version": "29.7.0", "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "@types/node": "*", "jest-util": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "quran-app/node_modules/jest-regex-util": {"version": "29.6.3", "license": "MIT", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "quran-app/node_modules/jest-util": {"version": "29.7.0", "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "ci-info": "^3.2.0", "graceful-fs": "^4.2.9", "picomatch": "^2.2.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "quran-app/node_modules/jest-util/node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "quran-app/node_modules/jest-util/node_modules/chalk": {"version": "4.1.2", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "quran-app/node_modules/jest-util/node_modules/ci-info": {"version": "3.9.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/sibir<PERSON>-s"}], "license": "MIT", "engines": {"node": ">=8"}}, "quran-app/node_modules/jest-util/node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "quran-app/node_modules/jest-util/node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "quran-app/node_modules/jest-util/node_modules/has-flag": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "quran-app/node_modules/jest-util/node_modules/supports-color": {"version": "7.2.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/jest-validate": {"version": "29.7.0", "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "camelcase": "^6.2.0", "chalk": "^4.0.0", "jest-get-type": "^29.6.3", "leven": "^3.1.0", "pretty-format": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "quran-app/node_modules/jest-validate/node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "quran-app/node_modules/jest-validate/node_modules/chalk": {"version": "4.1.2", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "quran-app/node_modules/jest-validate/node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "quran-app/node_modules/jest-validate/node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "quran-app/node_modules/jest-validate/node_modules/has-flag": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "quran-app/node_modules/jest-validate/node_modules/supports-color": {"version": "7.2.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/jest-worker": {"version": "29.7.0", "license": "MIT", "dependencies": {"@types/node": "*", "jest-util": "^29.7.0", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "quran-app/node_modules/jest-worker/node_modules/has-flag": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "quran-app/node_modules/jest-worker/node_modules/supports-color": {"version": "8.1.1", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "quran-app/node_modules/jimp-compact": {"version": "0.16.1", "license": "MIT"}, "quran-app/node_modules/js-tokens": {"version": "4.0.0", "license": "MIT"}, "quran-app/node_modules/js-yaml": {"version": "3.14.1", "license": "MIT", "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "quran-app/node_modules/jsc-safe-url": {"version": "0.2.4", "license": "0BSD"}, "quran-app/node_modules/jsesc": {"version": "3.1.0", "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=6"}}, "quran-app/node_modules/json-parse-better-errors": {"version": "1.0.2", "license": "MIT"}, "quran-app/node_modules/json5": {"version": "2.2.3", "license": "MIT", "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "quran-app/node_modules/kleur": {"version": "3.0.3", "license": "MIT", "engines": {"node": ">=6"}}, "quran-app/node_modules/lan-network": {"version": "0.1.7", "license": "MIT", "bin": {"lan-network": "dist/lan-network-cli.js"}}, "quran-app/node_modules/leven": {"version": "3.1.0", "license": "MIT", "engines": {"node": ">=6"}}, "quran-app/node_modules/lighthouse-logger": {"version": "1.4.2", "license": "Apache-2.0", "dependencies": {"debug": "^2.6.9", "marky": "^1.2.2"}}, "quran-app/node_modules/lighthouse-logger/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "quran-app/node_modules/lighthouse-logger/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "quran-app/node_modules/lightningcss": {"version": "1.30.2", "license": "MPL-2.0", "dependencies": {"detect-libc": "^2.0.3"}, "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "optionalDependencies": {"lightningcss-android-arm64": "1.30.2", "lightningcss-darwin-arm64": "1.30.2", "lightningcss-darwin-x64": "1.30.2", "lightningcss-freebsd-x64": "1.30.2", "lightningcss-linux-arm-gnueabihf": "1.30.2", "lightningcss-linux-arm64-gnu": "1.30.2", "lightningcss-linux-arm64-musl": "1.30.2", "lightningcss-linux-x64-gnu": "1.30.2", "lightningcss-linux-x64-musl": "1.30.2", "lightningcss-win32-arm64-msvc": "1.30.2", "lightningcss-win32-x64-msvc": "1.30.2"}}, "quran-app/node_modules/lightningcss-win32-x64-msvc": {"version": "1.30.2", "cpu": ["x64"], "license": "MPL-2.0", "optional": true, "os": ["win32"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "quran-app/node_modules/lines-and-columns": {"version": "1.2.4", "license": "MIT"}, "quran-app/node_modules/locate-path": {"version": "5.0.0", "license": "MIT", "dependencies": {"p-locate": "^4.1.0"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/lodash.debounce": {"version": "4.0.8", "license": "MIT"}, "quran-app/node_modules/lodash.throttle": {"version": "4.1.1", "license": "MIT"}, "quran-app/node_modules/log-symbols": {"version": "2.2.0", "license": "MIT", "dependencies": {"chalk": "^2.0.1"}, "engines": {"node": ">=4"}}, "quran-app/node_modules/loose-envify": {"version": "1.4.0", "license": "MIT", "dependencies": {"js-tokens": "^3.0.0 || ^4.0.0"}, "bin": {"loose-envify": "cli.js"}}, "quran-app/node_modules/lru-cache": {"version": "5.1.1", "license": "ISC", "dependencies": {"yallist": "^3.0.2"}}, "quran-app/node_modules/makeerror": {"version": "1.0.12", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"tmpl": "1.0.5"}}, "quran-app/node_modules/marky": {"version": "1.3.0", "license": "Apache-2.0"}, "quran-app/node_modules/memoize-one": {"version": "5.2.1", "license": "MIT"}, "quran-app/node_modules/merge-stream": {"version": "2.0.0", "license": "MIT"}, "quran-app/node_modules/metro": {"version": "0.83.1", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.24.7", "@babel/core": "^7.25.2", "@babel/generator": "^7.25.0", "@babel/parser": "^7.25.3", "@babel/template": "^7.25.0", "@babel/traverse": "^7.25.3", "@babel/types": "^7.25.2", "accepts": "^1.3.7", "chalk": "^4.0.0", "ci-info": "^2.0.0", "connect": "^3.6.5", "debug": "^4.4.0", "error-stack-parser": "^2.0.6", "flow-enums-runtime": "^0.0.6", "graceful-fs": "^4.2.4", "hermes-parser": "0.29.1", "image-size": "^1.0.2", "invariant": "^2.2.4", "jest-worker": "^29.7.0", "jsc-safe-url": "^0.2.2", "lodash.throttle": "^4.1.1", "metro-babel-transformer": "0.83.1", "metro-cache": "0.83.1", "metro-cache-key": "0.83.1", "metro-config": "0.83.1", "metro-core": "0.83.1", "metro-file-map": "0.83.1", "metro-resolver": "0.83.1", "metro-runtime": "0.83.1", "metro-source-map": "0.83.1", "metro-symbolicate": "0.83.1", "metro-transform-plugins": "0.83.1", "metro-transform-worker": "0.83.1", "mime-types": "^2.1.27", "nullthrows": "^1.1.1", "serialize-error": "^2.1.0", "source-map": "^0.5.6", "throat": "^5.0.0", "ws": "^7.5.10", "yargs": "^17.6.2"}, "bin": {"metro": "src/cli.js"}, "engines": {"node": ">=20.19.4"}}, "quran-app/node_modules/metro-babel-transformer": {"version": "0.83.1", "license": "MIT", "dependencies": {"@babel/core": "^7.25.2", "flow-enums-runtime": "^0.0.6", "hermes-parser": "0.29.1", "nullthrows": "^1.1.1"}, "engines": {"node": ">=20.19.4"}}, "quran-app/node_modules/metro-cache": {"version": "0.83.1", "license": "MIT", "dependencies": {"exponential-backoff": "^3.1.1", "flow-enums-runtime": "^0.0.6", "https-proxy-agent": "^7.0.5", "metro-core": "0.83.1"}, "engines": {"node": ">=20.19.4"}}, "quran-app/node_modules/metro-cache-key": {"version": "0.83.1", "license": "MIT", "dependencies": {"flow-enums-runtime": "^0.0.6"}, "engines": {"node": ">=20.19.4"}}, "quran-app/node_modules/metro-config": {"version": "0.83.1", "license": "MIT", "dependencies": {"connect": "^3.6.5", "cosmiconfig": "^5.0.5", "flow-enums-runtime": "^0.0.6", "jest-validate": "^29.7.0", "metro": "0.83.1", "metro-cache": "0.83.1", "metro-core": "0.83.1", "metro-runtime": "0.83.1"}, "engines": {"node": ">=20.19.4"}}, "quran-app/node_modules/metro-core": {"version": "0.83.1", "license": "MIT", "dependencies": {"flow-enums-runtime": "^0.0.6", "lodash.throttle": "^4.1.1", "metro-resolver": "0.83.1"}, "engines": {"node": ">=20.19.4"}}, "quran-app/node_modules/metro-file-map": {"version": "0.83.1", "license": "MIT", "dependencies": {"debug": "^4.4.0", "fb-watchman": "^2.0.0", "flow-enums-runtime": "^0.0.6", "graceful-fs": "^4.2.4", "invariant": "^2.2.4", "jest-worker": "^29.7.0", "micromatch": "^4.0.4", "nullthrows": "^1.1.1", "walker": "^1.0.7"}, "engines": {"node": ">=20.19.4"}}, "quran-app/node_modules/metro-minify-terser": {"version": "0.83.1", "license": "MIT", "dependencies": {"flow-enums-runtime": "^0.0.6", "terser": "^5.15.0"}, "engines": {"node": ">=20.19.4"}}, "quran-app/node_modules/metro-resolver": {"version": "0.83.1", "license": "MIT", "dependencies": {"flow-enums-runtime": "^0.0.6"}, "engines": {"node": ">=20.19.4"}}, "quran-app/node_modules/metro-runtime": {"version": "0.83.1", "license": "MIT", "dependencies": {"@babel/runtime": "^7.25.0", "flow-enums-runtime": "^0.0.6"}, "engines": {"node": ">=20.19.4"}}, "quran-app/node_modules/metro-source-map": {"version": "0.83.1", "license": "MIT", "dependencies": {"@babel/traverse": "^7.25.3", "@babel/traverse--for-generate-function-map": "npm:@babel/traverse@^7.25.3", "@babel/types": "^7.25.2", "flow-enums-runtime": "^0.0.6", "invariant": "^2.2.4", "metro-symbolicate": "0.83.1", "nullthrows": "^1.1.1", "ob1": "0.83.1", "source-map": "^0.5.6", "vlq": "^1.0.0"}, "engines": {"node": ">=20.19.4"}}, "quran-app/node_modules/metro-symbolicate": {"version": "0.83.1", "license": "MIT", "dependencies": {"flow-enums-runtime": "^0.0.6", "invariant": "^2.2.4", "metro-source-map": "0.83.1", "nullthrows": "^1.1.1", "source-map": "^0.5.6", "vlq": "^1.0.0"}, "bin": {"metro-symbolicate": "src/index.js"}, "engines": {"node": ">=20.19.4"}}, "quran-app/node_modules/metro-transform-plugins": {"version": "0.83.1", "license": "MIT", "dependencies": {"@babel/core": "^7.25.2", "@babel/generator": "^7.25.0", "@babel/template": "^7.25.0", "@babel/traverse": "^7.25.3", "flow-enums-runtime": "^0.0.6", "nullthrows": "^1.1.1"}, "engines": {"node": ">=20.19.4"}}, "quran-app/node_modules/metro-transform-worker": {"version": "0.83.1", "license": "MIT", "dependencies": {"@babel/core": "^7.25.2", "@babel/generator": "^7.25.0", "@babel/parser": "^7.25.3", "@babel/types": "^7.25.2", "flow-enums-runtime": "^0.0.6", "metro": "0.83.1", "metro-babel-transformer": "0.83.1", "metro-cache": "0.83.1", "metro-cache-key": "0.83.1", "metro-minify-terser": "0.83.1", "metro-source-map": "0.83.1", "metro-transform-plugins": "0.83.1", "nullthrows": "^1.1.1"}, "engines": {"node": ">=20.19.4"}}, "quran-app/node_modules/metro/node_modules/@babel/code-frame": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "engines": {"node": ">=6.9.0"}}, "quran-app/node_modules/metro/node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "quran-app/node_modules/metro/node_modules/chalk": {"version": "4.1.2", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "quran-app/node_modules/metro/node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "quran-app/node_modules/metro/node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "quran-app/node_modules/metro/node_modules/has-flag": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "quran-app/node_modules/metro/node_modules/supports-color": {"version": "7.2.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/micromatch": {"version": "4.0.8", "license": "MIT", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "quran-app/node_modules/mime": {"version": "1.6.0", "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "quran-app/node_modules/mime-db": {"version": "1.52.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "quran-app/node_modules/mime-types": {"version": "2.1.35", "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "quran-app/node_modules/mimic-fn": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">=4"}}, "quran-app/node_modules/minimatch": {"version": "9.0.5", "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "quran-app/node_modules/minimist": {"version": "1.2.8", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "quran-app/node_modules/minipass": {"version": "7.1.2", "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}}, "quran-app/node_modules/minizlib": {"version": "3.1.0", "license": "MIT", "dependencies": {"minipass": "^7.1.2"}, "engines": {"node": ">= 18"}}, "quran-app/node_modules/mkdirp": {"version": "1.0.4", "license": "MIT", "bin": {"mkdirp": "bin/cmd.js"}, "engines": {"node": ">=10"}}, "quran-app/node_modules/ms": {"version": "2.1.3", "license": "MIT"}, "quran-app/node_modules/mz": {"version": "2.7.0", "license": "MIT", "dependencies": {"any-promise": "^1.0.0", "object-assign": "^4.0.1", "thenify-all": "^1.0.0"}}, "quran-app/node_modules/nanoid": {"version": "3.3.11", "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "quran-app/node_modules/negotiator": {"version": "0.6.3", "license": "MIT", "engines": {"node": ">= 0.6"}}, "quran-app/node_modules/nested-error-stacks": {"version": "2.0.1", "license": "MIT"}, "quran-app/node_modules/node-forge": {"version": "1.3.1", "license": "(BSD-3-<PERSON><PERSON> OR GPL-2.0)", "engines": {"node": ">= 6.13.0"}}, "quran-app/node_modules/node-int64": {"version": "0.4.0", "license": "MIT"}, "quran-app/node_modules/node-releases": {"version": "2.0.23", "license": "MIT"}, "quran-app/node_modules/normalize-path": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "quran-app/node_modules/npm-package-arg": {"version": "11.0.3", "license": "ISC", "dependencies": {"hosted-git-info": "^7.0.0", "proc-log": "^4.0.0", "semver": "^7.3.5", "validate-npm-package-name": "^5.0.0"}, "engines": {"node": "^16.14.0 || >=18.0.0"}}, "quran-app/node_modules/nullthrows": {"version": "1.1.1", "license": "MIT"}, "quran-app/node_modules/ob1": {"version": "0.83.1", "license": "MIT", "dependencies": {"flow-enums-runtime": "^0.0.6"}, "engines": {"node": ">=20.19.4"}}, "quran-app/node_modules/object-assign": {"version": "4.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "quran-app/node_modules/on-finished": {"version": "2.3.0", "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "quran-app/node_modules/on-headers": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "quran-app/node_modules/once": {"version": "1.4.0", "license": "ISC", "dependencies": {"wrappy": "1"}}, "quran-app/node_modules/onetime": {"version": "2.0.1", "license": "MIT", "dependencies": {"mimic-fn": "^1.0.0"}, "engines": {"node": ">=4"}}, "quran-app/node_modules/open": {"version": "7.4.2", "license": "MIT", "dependencies": {"is-docker": "^2.0.0", "is-wsl": "^2.1.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "quran-app/node_modules/ora": {"version": "3.4.0", "license": "MIT", "dependencies": {"chalk": "^2.4.2", "cli-cursor": "^2.1.0", "cli-spinners": "^2.0.0", "log-symbols": "^2.2.0", "strip-ansi": "^5.2.0", "wcwidth": "^1.0.1"}, "engines": {"node": ">=6"}}, "quran-app/node_modules/ora/node_modules/ansi-regex": {"version": "4.1.1", "license": "MIT", "engines": {"node": ">=6"}}, "quran-app/node_modules/ora/node_modules/strip-ansi": {"version": "5.2.0", "license": "MIT", "dependencies": {"ansi-regex": "^4.1.0"}, "engines": {"node": ">=6"}}, "quran-app/node_modules/p-limit": {"version": "3.1.0", "license": "MIT", "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "quran-app/node_modules/p-locate": {"version": "4.1.0", "license": "MIT", "dependencies": {"p-limit": "^2.2.0"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/p-locate/node_modules/p-limit": {"version": "2.3.0", "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "quran-app/node_modules/p-try": {"version": "2.2.0", "license": "MIT", "engines": {"node": ">=6"}}, "quran-app/node_modules/package-json-from-dist": {"version": "1.0.1", "license": "BlueOak-1.0.0"}, "quran-app/node_modules/parse-json": {"version": "4.0.0", "license": "MIT", "dependencies": {"error-ex": "^1.3.1", "json-parse-better-errors": "^1.0.1"}, "engines": {"node": ">=4"}}, "quran-app/node_modules/parse-png": {"version": "2.1.0", "license": "MIT", "dependencies": {"pngjs": "^3.3.0"}, "engines": {"node": ">=10"}}, "quran-app/node_modules/parseurl": {"version": "1.3.3", "license": "MIT", "engines": {"node": ">= 0.8"}}, "quran-app/node_modules/path-exists": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "quran-app/node_modules/path-is-absolute": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "quran-app/node_modules/path-key": {"version": "3.1.1", "license": "MIT", "engines": {"node": ">=8"}}, "quran-app/node_modules/path-parse": {"version": "1.0.7", "license": "MIT"}, "quran-app/node_modules/path-scurry": {"version": "1.11.1", "license": "BlueOak-1.0.0", "dependencies": {"lru-cache": "^10.2.0", "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0"}, "engines": {"node": ">=16 || 14 >=14.18"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "quran-app/node_modules/path-scurry/node_modules/lru-cache": {"version": "10.4.3", "license": "ISC"}, "quran-app/node_modules/picocolors": {"version": "1.1.1", "license": "ISC"}, "quran-app/node_modules/picomatch": {"version": "2.3.1", "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "quran-app/node_modules/pirates": {"version": "4.0.7", "license": "MIT", "engines": {"node": ">= 6"}}, "quran-app/node_modules/plist": {"version": "3.1.0", "license": "MIT", "dependencies": {"@xmldom/xmldom": "^0.8.8", "base64-js": "^1.5.1", "xmlbuilder": "^15.1.1"}, "engines": {"node": ">=10.4.0"}}, "quran-app/node_modules/pngjs": {"version": "3.4.0", "license": "MIT", "engines": {"node": ">=4.0.0"}}, "quran-app/node_modules/postcss": {"version": "8.4.49", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.7", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "engines": {"node": "^10 || ^12 || >=14"}}, "quran-app/node_modules/pretty-bytes": {"version": "5.6.0", "license": "MIT", "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "quran-app/node_modules/pretty-format": {"version": "29.7.0", "license": "MIT", "dependencies": {"@jest/schemas": "^29.6.3", "ansi-styles": "^5.0.0", "react-is": "^18.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "quran-app/node_modules/pretty-format/node_modules/ansi-styles": {"version": "5.2.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "quran-app/node_modules/proc-log": {"version": "4.2.0", "license": "ISC", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "quran-app/node_modules/progress": {"version": "2.0.3", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "quran-app/node_modules/promise": {"version": "8.3.0", "license": "MIT", "dependencies": {"asap": "~2.0.6"}}, "quran-app/node_modules/prompts": {"version": "2.4.2", "license": "MIT", "dependencies": {"kleur": "^3.0.3", "sisteransi": "^1.0.5"}, "engines": {"node": ">= 6"}}, "quran-app/node_modules/punycode": {"version": "2.3.1", "license": "MIT", "engines": {"node": ">=6"}}, "quran-app/node_modules/qrcode-terminal": {"version": "0.11.0", "bin": {"qrcode-terminal": "bin/qrcode-terminal.js"}}, "quran-app/node_modules/queue": {"version": "6.0.2", "license": "MIT", "dependencies": {"inherits": "~2.0.3"}}, "quran-app/node_modules/range-parser": {"version": "1.2.1", "license": "MIT", "engines": {"node": ">= 0.6"}}, "quran-app/node_modules/rc": {"version": "1.2.8", "license": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "dependencies": {"deep-extend": "^0.6.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "bin": {"rc": "cli.js"}}, "quran-app/node_modules/react": {"version": "19.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "quran-app/node_modules/react-devtools-core": {"version": "6.1.5", "license": "MIT", "dependencies": {"shell-quote": "^1.6.1", "ws": "^7"}}, "quran-app/node_modules/react-is": {"version": "18.3.1", "license": "MIT"}, "quran-app/node_modules/react-native": {"version": "0.81.4", "license": "MIT", "dependencies": {"@jest/create-cache-key-function": "^29.7.0", "@react-native/assets-registry": "0.81.4", "@react-native/codegen": "0.81.4", "@react-native/community-cli-plugin": "0.81.4", "@react-native/gradle-plugin": "0.81.4", "@react-native/js-polyfills": "0.81.4", "@react-native/normalize-colors": "0.81.4", "@react-native/virtualized-lists": "0.81.4", "abort-controller": "^3.0.0", "anser": "^1.4.9", "ansi-regex": "^5.0.0", "babel-jest": "^29.7.0", "babel-plugin-syntax-hermes-parser": "0.29.1", "base64-js": "^1.5.1", "commander": "^12.0.0", "flow-enums-runtime": "^0.0.6", "glob": "^7.1.1", "invariant": "^2.2.4", "jest-environment-node": "^29.7.0", "memoize-one": "^5.0.0", "metro-runtime": "^0.83.1", "metro-source-map": "^0.83.1", "nullthrows": "^1.1.1", "pretty-format": "^29.7.0", "promise": "^8.3.0", "react-devtools-core": "^6.1.5", "react-refresh": "^0.14.0", "regenerator-runtime": "^0.13.2", "scheduler": "0.26.0", "semver": "^7.1.3", "stacktrace-parser": "^0.1.10", "whatwg-fetch": "^3.0.0", "ws": "^6.2.3", "yargs": "^17.6.2"}, "bin": {"react-native": "cli.js"}, "engines": {"node": ">= 20.19.4"}, "peerDependencies": {"@types/react": "^19.1.0", "react": "^19.1.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "quran-app/node_modules/react-native-is-edge-to-edge": {"version": "1.2.1", "license": "MIT", "peerDependencies": {"react": "*", "react-native": "*"}}, "quran-app/node_modules/react-native/node_modules/brace-expansion": {"version": "1.1.12", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "quran-app/node_modules/react-native/node_modules/commander": {"version": "12.1.0", "license": "MIT", "engines": {"node": ">=18"}}, "quran-app/node_modules/react-native/node_modules/glob": {"version": "7.2.3", "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "quran-app/node_modules/react-native/node_modules/minimatch": {"version": "3.1.2", "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "quran-app/node_modules/react-native/node_modules/ws": {"version": "6.2.3", "license": "MIT", "dependencies": {"async-limiter": "~1.0.0"}}, "quran-app/node_modules/react-refresh": {"version": "0.14.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "quran-app/node_modules/regenerate": {"version": "1.4.2", "license": "MIT"}, "quran-app/node_modules/regenerator-runtime": {"version": "0.13.11", "license": "MIT"}, "quran-app/node_modules/regexpu-core": {"version": "6.4.0", "license": "MIT", "dependencies": {"regenerate": "^1.4.2", "regenerate-unicode-properties": "^10.2.2", "regjsgen": "^0.8.0", "regjsparser": "^0.13.0", "unicode-match-property-ecmascript": "^2.0.0", "unicode-match-property-value-ecmascript": "^2.2.1"}, "engines": {"node": ">=4"}}, "quran-app/node_modules/regexpu-core/node_modules/regenerate-unicode-properties": {"version": "10.2.2", "license": "MIT", "dependencies": {"regenerate": "^1.4.2"}, "engines": {"node": ">=4"}}, "quran-app/node_modules/regjsgen": {"version": "0.8.0", "license": "MIT"}, "quran-app/node_modules/regjsparser": {"version": "0.13.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"jsesc": "~3.1.0"}, "bin": {"regjsparser": "bin/parser"}}, "quran-app/node_modules/require-directory": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "quran-app/node_modules/require-from-string": {"version": "2.0.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "quran-app/node_modules/requireg": {"version": "0.2.2", "dependencies": {"nested-error-stacks": "~2.0.1", "rc": "~1.2.7", "resolve": "~1.7.1"}, "engines": {"node": ">= 4.0.0"}}, "quran-app/node_modules/requireg/node_modules/resolve": {"version": "1.7.1", "license": "MIT", "dependencies": {"path-parse": "^1.0.5"}}, "quran-app/node_modules/resolve": {"version": "1.22.10", "license": "MIT", "dependencies": {"is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "quran-app/node_modules/resolve-from": {"version": "5.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "quran-app/node_modules/resolve-global": {"version": "1.0.0", "license": "MIT", "dependencies": {"global-dirs": "^0.1.1"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/resolve-workspace-root": {"version": "2.0.0", "license": "MIT"}, "quran-app/node_modules/resolve.exports": {"version": "2.0.3", "license": "MIT", "engines": {"node": ">=10"}}, "quran-app/node_modules/restore-cursor": {"version": "2.0.0", "license": "MIT", "dependencies": {"onetime": "^2.0.0", "signal-exit": "^3.0.2"}, "engines": {"node": ">=4"}}, "quran-app/node_modules/restore-cursor/node_modules/signal-exit": {"version": "3.0.7", "license": "ISC"}, "quran-app/node_modules/rimraf": {"version": "3.0.2", "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "quran-app/node_modules/rimraf/node_modules/brace-expansion": {"version": "1.1.12", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "quran-app/node_modules/rimraf/node_modules/glob": {"version": "7.2.3", "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "quran-app/node_modules/rimraf/node_modules/minimatch": {"version": "3.1.2", "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "quran-app/node_modules/safe-buffer": {"version": "5.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "quran-app/node_modules/sax": {"version": "1.4.1", "license": "ISC"}, "quran-app/node_modules/scheduler": {"version": "0.26.0", "license": "MIT"}, "quran-app/node_modules/semver": {"version": "7.7.2", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "quran-app/node_modules/send": {"version": "0.19.0", "license": "MIT", "dependencies": {"debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "2.0.0", "mime": "1.6.0", "ms": "2.1.3", "on-finished": "2.4.1", "range-parser": "~1.2.1", "statuses": "2.0.1"}, "engines": {"node": ">= 0.8.0"}}, "quran-app/node_modules/send/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "quran-app/node_modules/send/node_modules/debug/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "quran-app/node_modules/send/node_modules/on-finished": {"version": "2.4.1", "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "quran-app/node_modules/send/node_modules/statuses": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">= 0.8"}}, "quran-app/node_modules/serialize-error": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "quran-app/node_modules/serve-static": {"version": "1.16.2", "license": "MIT", "dependencies": {"encodeurl": "~2.0.0", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.19.0"}, "engines": {"node": ">= 0.8.0"}}, "quran-app/node_modules/serve-static/node_modules/encodeurl": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "quran-app/node_modules/setprototypeof": {"version": "1.2.0", "license": "ISC"}, "quran-app/node_modules/shebang-command": {"version": "2.0.0", "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/shebang-regex": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "quran-app/node_modules/shell-quote": {"version": "1.8.3", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "quran-app/node_modules/signal-exit": {"version": "4.1.0", "license": "ISC", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "quran-app/node_modules/simple-plist": {"version": "1.3.1", "license": "MIT", "dependencies": {"bplist-creator": "0.1.0", "bplist-parser": "0.3.1", "plist": "^3.0.5"}}, "quran-app/node_modules/sisteransi": {"version": "1.0.5", "license": "MIT"}, "quran-app/node_modules/slash": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "quran-app/node_modules/slugify": {"version": "1.6.6", "license": "MIT", "engines": {"node": ">=8.0.0"}}, "quran-app/node_modules/source-map": {"version": "0.5.7", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "quran-app/node_modules/source-map-js": {"version": "1.2.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "quran-app/node_modules/source-map-support": {"version": "0.5.21", "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "quran-app/node_modules/source-map-support/node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "quran-app/node_modules/sprintf-js": {"version": "1.0.3", "license": "BSD-3-<PERSON><PERSON>"}, "quran-app/node_modules/stack-utils": {"version": "2.0.6", "license": "MIT", "dependencies": {"escape-string-regexp": "^2.0.0"}, "engines": {"node": ">=10"}}, "quran-app/node_modules/stack-utils/node_modules/escape-string-regexp": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "quran-app/node_modules/stackframe": {"version": "1.3.4", "license": "MIT"}, "quran-app/node_modules/stacktrace-parser": {"version": "0.1.11", "license": "MIT", "dependencies": {"type-fest": "^0.7.1"}, "engines": {"node": ">=6"}}, "quran-app/node_modules/statuses": {"version": "1.5.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "quran-app/node_modules/stream-buffers": {"version": "2.2.0", "license": "Unlicense", "engines": {"node": ">= 0.10.0"}}, "quran-app/node_modules/string-width": {"version": "5.1.2", "license": "MIT", "dependencies": {"eastasianwidth": "^0.2.0", "emoji-regex": "^9.2.2", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "quran-app/node_modules/string-width-cjs": {"name": "string-width", "version": "4.2.3", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/string-width-cjs/node_modules/emoji-regex": {"version": "8.0.0", "license": "MIT"}, "quran-app/node_modules/string-width-cjs/node_modules/strip-ansi": {"version": "6.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/strip-ansi": {"version": "7.1.2", "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "quran-app/node_modules/strip-ansi-cjs": {"name": "strip-ansi", "version": "6.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/strip-ansi/node_modules/ansi-regex": {"version": "6.2.2", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "quran-app/node_modules/strip-json-comments": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "quran-app/node_modules/structured-headers": {"version": "0.4.1", "license": "MIT"}, "quran-app/node_modules/sucrase": {"version": "3.35.0", "license": "MIT", "dependencies": {"@jridgewell/gen-mapping": "^0.3.2", "commander": "^4.0.0", "glob": "^10.3.10", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "bin": {"sucrase": "bin/sucrase", "sucrase-node": "bin/sucrase-node"}, "engines": {"node": ">=16 || 14 >=14.17"}}, "quran-app/node_modules/sucrase/node_modules/commander": {"version": "4.1.1", "license": "MIT", "engines": {"node": ">= 6"}}, "quran-app/node_modules/supports-color": {"version": "5.5.0", "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "quran-app/node_modules/supports-hyperlinks": {"version": "2.3.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0", "supports-color": "^7.0.0"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/supports-hyperlinks/node_modules/has-flag": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "quran-app/node_modules/supports-hyperlinks/node_modules/supports-color": {"version": "7.2.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "quran-app/node_modules/tar": {"version": "7.5.1", "license": "ISC", "dependencies": {"@isaacs/fs-minipass": "^4.0.0", "chownr": "^3.0.0", "minipass": "^7.1.2", "minizlib": "^3.1.0", "yallist": "^5.0.0"}, "engines": {"node": ">=18"}}, "quran-app/node_modules/tar/node_modules/yallist": {"version": "5.0.0", "license": "BlueOak-1.0.0", "engines": {"node": ">=18"}}, "quran-app/node_modules/temp-dir": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "quran-app/node_modules/terminal-link": {"version": "2.1.1", "license": "MIT", "dependencies": {"ansi-escapes": "^4.2.1", "supports-hyperlinks": "^2.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "quran-app/node_modules/terser": {"version": "5.44.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@jridgewell/source-map": "^0.3.3", "acorn": "^8.15.0", "commander": "^2.20.0", "source-map-support": "~0.5.20"}, "bin": {"terser": "bin/terser"}, "engines": {"node": ">=10"}}, "quran-app/node_modules/terser/node_modules/commander": {"version": "2.20.3", "license": "MIT"}, "quran-app/node_modules/test-exclude": {"version": "6.0.0", "license": "ISC", "dependencies": {"@istanbuljs/schema": "^0.1.2", "glob": "^7.1.4", "minimatch": "^3.0.4"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/test-exclude/node_modules/brace-expansion": {"version": "1.1.12", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "quran-app/node_modules/test-exclude/node_modules/glob": {"version": "7.2.3", "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "quran-app/node_modules/test-exclude/node_modules/minimatch": {"version": "3.1.2", "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "quran-app/node_modules/thenify": {"version": "3.3.1", "license": "MIT", "dependencies": {"any-promise": "^1.0.0"}}, "quran-app/node_modules/thenify-all": {"version": "1.6.0", "license": "MIT", "dependencies": {"thenify": ">= 3.1.0 < 4"}, "engines": {"node": ">=0.8"}}, "quran-app/node_modules/throat": {"version": "5.0.0", "license": "MIT"}, "quran-app/node_modules/tmpl": {"version": "1.0.5", "license": "BSD-3-<PERSON><PERSON>"}, "quran-app/node_modules/to-regex-range": {"version": "5.0.1", "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "quran-app/node_modules/toidentifier": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.6"}}, "quran-app/node_modules/ts-interface-checker": {"version": "0.1.13", "license": "Apache-2.0"}, "quran-app/node_modules/type-detect": {"version": "4.0.8", "license": "MIT", "engines": {"node": ">=4"}}, "quran-app/node_modules/type-fest": {"version": "0.7.1", "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=8"}}, "quran-app/node_modules/undici": {"version": "6.22.0", "license": "MIT", "engines": {"node": ">=18.17"}}, "quran-app/node_modules/undici-types": {"version": "7.13.0", "license": "MIT"}, "quran-app/node_modules/unicode-canonical-property-names-ecmascript": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">=4"}}, "quran-app/node_modules/unicode-match-property-ecmascript": {"version": "2.0.0", "license": "MIT", "dependencies": {"unicode-canonical-property-names-ecmascript": "^2.0.0", "unicode-property-aliases-ecmascript": "^2.0.0"}, "engines": {"node": ">=4"}}, "quran-app/node_modules/unicode-match-property-value-ecmascript": {"version": "2.2.1", "license": "MIT", "engines": {"node": ">=4"}}, "quran-app/node_modules/unicode-property-aliases-ecmascript": {"version": "2.2.0", "license": "MIT", "engines": {"node": ">=4"}}, "quran-app/node_modules/unique-string": {"version": "2.0.0", "license": "MIT", "dependencies": {"crypto-random-string": "^2.0.0"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/unpipe": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "quran-app/node_modules/update-browserslist-db": {"version": "1.1.3", "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}, "bin": {"update-browserslist-db": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "quran-app/node_modules/utils-merge": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "quran-app/node_modules/uuid": {"version": "7.0.3", "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "quran-app/node_modules/validate-npm-package-name": {"version": "5.0.1", "license": "ISC", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "quran-app/node_modules/vary": {"version": "1.1.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "quran-app/node_modules/vlq": {"version": "1.0.1", "license": "MIT"}, "quran-app/node_modules/walker": {"version": "1.0.8", "license": "Apache-2.0", "dependencies": {"makeerror": "1.0.12"}}, "quran-app/node_modules/wcwidth": {"version": "1.0.1", "license": "MIT", "dependencies": {"defaults": "^1.0.3"}}, "quran-app/node_modules/webidl-conversions": {"version": "5.0.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=8"}}, "quran-app/node_modules/whatwg-fetch": {"version": "3.6.20", "license": "MIT"}, "quran-app/node_modules/whatwg-url-without-unicode": {"version": "8.0.0-3", "license": "MIT", "dependencies": {"buffer": "^5.4.3", "punycode": "^2.1.1", "webidl-conversions": "^5.0.0"}, "engines": {"node": ">=10"}}, "quran-app/node_modules/which": {"version": "2.0.2", "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "quran-app/node_modules/wonka": {"version": "6.3.5", "license": "MIT"}, "quran-app/node_modules/wrap-ansi": {"version": "8.1.0", "license": "MIT", "dependencies": {"ansi-styles": "^6.1.0", "string-width": "^5.0.1", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "quran-app/node_modules/wrap-ansi-cjs": {"name": "wrap-ansi", "version": "7.0.0", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "quran-app/node_modules/wrap-ansi-cjs/node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "quran-app/node_modules/wrap-ansi-cjs/node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "quran-app/node_modules/wrap-ansi-cjs/node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "quran-app/node_modules/wrap-ansi-cjs/node_modules/emoji-regex": {"version": "8.0.0", "license": "MIT"}, "quran-app/node_modules/wrap-ansi-cjs/node_modules/string-width": {"version": "4.2.3", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/wrap-ansi-cjs/node_modules/strip-ansi": {"version": "6.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/wrap-ansi/node_modules/ansi-styles": {"version": "6.2.3", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "quran-app/node_modules/wrappy": {"version": "1.0.2", "license": "ISC"}, "quran-app/node_modules/write-file-atomic": {"version": "4.0.2", "license": "ISC", "dependencies": {"imurmurhash": "^0.1.4", "signal-exit": "^3.0.7"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "quran-app/node_modules/write-file-atomic/node_modules/signal-exit": {"version": "3.0.7", "license": "ISC"}, "quran-app/node_modules/ws": {"version": "7.5.10", "license": "MIT", "engines": {"node": ">=8.3.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "quran-app/node_modules/xcode": {"version": "3.0.1", "license": "Apache-2.0", "dependencies": {"simple-plist": "^1.1.0", "uuid": "^7.0.3"}, "engines": {"node": ">=10.0.0"}}, "quran-app/node_modules/xml2js": {"version": "0.6.0", "license": "MIT", "dependencies": {"sax": ">=0.6.0", "xmlbuilder": "~11.0.0"}, "engines": {"node": ">=4.0.0"}}, "quran-app/node_modules/xml2js/node_modules/xmlbuilder": {"version": "11.0.1", "license": "MIT", "engines": {"node": ">=4.0"}}, "quran-app/node_modules/xmlbuilder": {"version": "15.1.1", "license": "MIT", "engines": {"node": ">=8.0"}}, "quran-app/node_modules/y18n": {"version": "5.0.8", "license": "ISC", "engines": {"node": ">=10"}}, "quran-app/node_modules/yallist": {"version": "3.1.1", "license": "ISC"}, "quran-app/node_modules/yargs": {"version": "17.7.2", "license": "MIT", "dependencies": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}, "engines": {"node": ">=12"}}, "quran-app/node_modules/yargs-parser": {"version": "21.1.1", "license": "ISC", "engines": {"node": ">=12"}}, "quran-app/node_modules/yargs/node_modules/emoji-regex": {"version": "8.0.0", "license": "MIT"}, "quran-app/node_modules/yargs/node_modules/string-width": {"version": "4.2.3", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/yargs/node_modules/strip-ansi": {"version": "6.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "quran-app/node_modules/yocto-queue": {"version": "0.1.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "quran-app/node_modules/zod": {"version": "3.25.76", "license": "MIT", "funding": {"url": "https://github.com/sponsors/colinhacks"}}, "quran-app/node_modules/zod-to-json-schema": {"version": "3.24.6", "license": "ISC", "peerDependencies": {"zod": "^3.24.1"}}}}