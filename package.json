{"name": "quran-app-workspace", "version": "1.0.0", "description": "تطبيق القرآن الكريم - Expo React Native", "main": "quran-app/index.js", "scripts": {"start": "cd quran-app && npm start", "android": "cd quran-app && npm run android", "ios": "cd quran-app && npm run ios", "web": "cd quran-app && npm run web", "install-deps": "cd quran-app && npm install", "setup": "cd quran-app && npm install && npm start"}, "keywords": ["quran", "islamic", "react-native", "expo", "mobile-app", "arabic"], "author": "مطور التطبيق", "license": "MIT", "repository": {"type": "git", "url": "."}, "workspaces": ["quran-app"]}