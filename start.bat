@echo off
echo ========================================
echo       تطبيق القرآن الكريم
echo ========================================
echo.
echo بسم الله الرحمن الرحيم
echo.

REM التحقق من وجود Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Node.js غير مثبت!
    echo يرجى تثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

echo Node.js مثبت ✓
echo.

REM الانتقال إلى مجلد التطبيق
if not exist "quran-app" (
    echo خطأ: مجلد quran-app غير موجود!
    pause
    exit /b 1
)

cd quran-app

REM تثبيت المكتبات إذا لم تكن مثبتة
if not exist "node_modules" (
    echo تثبيت المكتبات المطلوبة...
    echo هذا قد يستغرق بضع دقائق...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo خطأ في تثبيت المكتبات!
        pause
        exit /b 1
    )
    echo.
    echo تم تثبيت المكتبات بنجاح ✓
    echo.
)

REM تشغيل التطبيق
echo تشغيل التطبيق...
echo.
echo ملاحظات مهمة:
echo - ستفتح صفحة في المتصفح مع QR Code
echo - ثبت تطبيق Expo Go على هاتفك الأندرويد
echo - امسح QR Code أو اضغط "Run on Android"
echo.
echo جاري التشغيل...
echo.

npm start

echo.
echo تم إغلاق التطبيق
pause
