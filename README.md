# 📱 تطبيق القرآن الكريم

## بسم الله الرحمن الرحيم

تطبيق موبايل للقرآن الكريم مطور باستخدام Expo React Native، يتضمن عرض صفحات المصحف وتشغيل التلاوات الصوتية.

## 🎯 المميزات

- 📖 عرض صفحات القرآن الكريم بخط واضح
- 🎵 تشغيل التلاوات الصوتية لعدة قراء مشهورين
- 📱 واجهة مستخدم جميلة ومتجاوبة باللغة العربية
- 🔍 فهرس السور مع معلومات كل سورة
- ⚙️ إعدادات التطبيق واختيار القارئ
- 🎨 تصميم إسلامي أنيق

## 🚀 التشغيل السريع

### الطريقة الأسرع:

```bash
# تشغيل مباشر
npm run setup
```

### أو خطوة بخطوة:

```bash
# 1. تثبيت المكتبات
npm run install-deps

# 2. تشغيل التطبيق
npm start
```

### أو باستخدام ملفات التشغيل:

**Windows:**
```cmd
cd quran-app
start.bat
```

**Linux/Mac:**
```bash
cd quran-app
chmod +x start.sh
./start.sh
```

## 📱 الاستخدام

1. **بعد تشغيل الأمر** ستفتح صفحة في المتصفح تحتوي على QR Code
2. **على هاتفك الأندرويد:**
   - ثبت تطبيق "Expo Go" من Google Play Store
   - افتح التطبيق وامسح الـ QR Code
3. **أو اضغط على "Run on Android device/emulator"**

## 📁 هيكل المشروع

```
القران الكريم/
├── package.json          # إعدادات المشروع الجذر
├── README.md            # هذا الملف
└── quran-app/           # التطبيق الرئيسي
    ├── App.js           # نقطة البداية
    ├── src/             # ملفات المصدر
    │   ├── QuranApp.js  # التطبيق الرئيسي
    │   ├── components/  # المكونات
    │   └── data/        # البيانات
    ├── assets/          # الأصول
    │   ├── audio/       # ملفات الصوت
    │   └── pages/       # صور الصفحات
    └── docs/            # التوثيق
```

## ✅ الحالة الحالية

### مكتمل:
- ✅ واجهة المستخدم الأساسية
- ✅ فهرس السور (10 سور)
- ✅ صفحة القراءة مع النصوص
- ✅ قائمة القراء (7 قراء)
- ✅ مشغل الصوت (الواجهة)
- ✅ تصميم متجاوب وجميل

### يحتاج إضافة:
- ⚠️ ملفات الصوت الفعلية (MP3)
- ⚠️ صور صفحات المصحف (PNG/JPG)
- ⚠️ تفعيل مكتبة الصوت expo-av

## 📚 إضافة المحتوى

### ملفات الصوت:
```
quran-app/assets/audio/
├── abdulbasit/
│   ├── 001.mp3  (الفاتحة)
│   ├── 002.mp3  (البقرة)
│   └── ...
├── minshawi/
├── ghamadi/
└── ...
```

### صور الصفحات:
```
quran-app/assets/pages/
├── page_001.png  (الصفحة الأولى)
├── page_002.png  (الصفحة الثانية)
└── ... (حتى page_604.png)
```

## 🔧 المتطلبات

- Node.js (الإصدار 16 أو أحدث)
- npm أو yarn
- Expo CLI
- Android Studio (للتطوير على الأندرويد)
- هاتف أندرويد مع تطبيق Expo Go

## 📖 التوثيق التفصيلي

للحصول على تعليمات مفصلة، راجع الملفات التالية في مجلد `quran-app/`:

- `QUICK_START.md` - دليل التشغيل السريع
- `INSTRUCTIONS.md` - تعليمات مفصلة
- `PROJECT_SUMMARY.md` - ملخص شامل للمشروع
- `CHECK_STATUS.md` - حالة التطبيق الحالية

## 🎵 القراء المتاحون

1. **عبد الباسط عبد الصمد** - مصر
2. **محمد صديق المنشاوي** - مصر  
3. **سعد الغامدي** - السعودية
4. **ماهر المعيقلي** - السعودية
5. **أحمد العجمي** - الكويت
6. **مشاري راشد العفاسي** - الكويت
7. **عبد الرحمن السديس** - السعودية

## 🛠️ التطوير

### إضافة قارئ جديد:
```javascript
// في src/data/quranData.js
{ 
  id: 8, 
  name: 'اسم القارئ', 
  folder: 'folder_name',
  description: 'وصف القارئ',
  country: 'البلد'
}
```

### تخصيص الألوان:
```javascript
// في src/QuranApp.js - قسم styles
primaryColor: '#1a4d3a',    // الأخضر الداكن
backgroundColor: '#f5f5f5',  // الرمادي الفاتح
textColor: '#333'           // النص الداكن
```

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام التعليمي والشخصي.

## 🆘 الدعم

للدعم والاستفسارات:
1. راجع ملفات التوثيق في `quran-app/`
2. تحقق من وجود الملفات المطلوبة
3. تأكد من تثبيت جميع المكتبات

---

**🎉 بارك الله فيك! استمتع بتطبيق القرآن الكريم**

*"وَنُنَزِّلُ مِنَ الْقُرْآنِ مَا هُوَ شِفَاءٌ وَرَحْمَةٌ لِّلْمُؤْمِنِينَ"*
