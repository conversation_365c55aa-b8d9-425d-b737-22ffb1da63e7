#!/bin/bash

echo "========================================"
echo "       تطبيق القرآن الكريم"
echo "========================================"
echo ""
echo "بسم الله الرحمن الرحيم"
echo ""

# التحقق من وجود Node.js
if ! command -v node &> /dev/null; then
    echo "خطأ: Node.js غير مثبت!"
    echo "يرجى تثبيت Node.js من: https://nodejs.org"
    exit 1
fi

echo "Node.js مثبت ✓"
echo ""

# التحقق من وجود مجلد التطبيق
if [ ! -d "quran-app" ]; then
    echo "خطأ: مجلد quran-app غير موجود!"
    exit 1
fi

cd quran-app

# تثبيت المكتبات إذا لم تكن مثبتة
if [ ! -d "node_modules" ]; then
    echo "تثبيت المكتبات المطلوبة..."
    echo "هذا قد يستغرق بضع دقائق..."
    echo ""
    npm install
    if [ $? -ne 0 ]; then
        echo "خطأ في تثبيت المكتبات!"
        exit 1
    fi
    echo ""
    echo "تم تثبيت المكتبات بنجاح ✓"
    echo ""
fi

# تشغيل التطبيق
echo "تشغيل التطبيق..."
echo ""
echo "ملاحظات مهمة:"
echo "- ستفتح صفحة في المتصفح مع QR Code"
echo "- ثبت تطبيق Expo Go على هاتفك الأندرويد"
echo "- امسح QR Code أو اضغط \"Run on Android\""
echo ""
echo "جاري التشغيل..."
echo ""

npm start

echo ""
echo "تم إغلاق التطبيق"
