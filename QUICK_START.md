# 🚀 تشغيل سريع - تطبيق القرآن الكريم

## بسم الله الرحمن الرحيم

## ⚡ التشغيل الفوري

### الطريقة الأسرع:

**Windows:**
```cmd
انقر مرتين على start.bat
```

**Linux/Mac:**
```bash
chmod +x start.sh
./start.sh
```

### أو باستخدام npm:
```bash
npm run setup
```

## 📱 بعد التشغيل

1. **ستفتح صفحة في المتصفح** تحتوي على QR Code
2. **على هاتفك الأندرويد:**
   - ثبت تطبيق "Expo Go" من Google Play Store
   - افتح التطبيق وامسح الـ QR Code
3. **أو اضغط على "Run on Android device/emulator"**

## ✅ ما ستراه في التطبيق

### يعمل حالياً:
- ✅ الشاشة الرئيسية الجميلة
- ✅ فهرس السور (10 سور مع التفاصيل)
- ✅ صفحات القراءة مع النصوص العربية
- ✅ قائمة القراء (7 قراء مشهورين)
- ✅ واجهة مشغل الصوت

### يحتاج إضافة:
- ⚠️ ملفات الصوت الفعلية (MP3)
- ⚠️ صور صفحات المصحف (PNG/JPG)

## 🎵 القراء المتاحون

1. عبد الباسط عبد الصمد
2. محمد صديق المنشاوي
3. سعد الغامدي
4. ماهر المعيقلي
5. أحمد العجمي
6. مشاري راشد العفاسي
7. عبد الرحمن السديس

## 📁 إضافة المحتوى (اختياري)

### للحصول على الصوت:
```
quran-app/assets/audio/abdulbasit/001.mp3  (الفاتحة)
quran-app/assets/audio/abdulbasit/002.mp3  (البقرة)
...
```

### للحصول على الصور:
```
quran-app/assets/pages/page_001.png  (الصفحة الأولى)
quran-app/assets/pages/page_002.png  (الصفحة الثانية)
...
```

## ❌ مشاكل شائعة

**"npm not found"**
- ثبت Node.js من nodejs.org

**"expo not found"**
```bash
npm install -g @expo/cli
```

**التطبيق لا يفتح على الهاتف**
- تأكد من اتصال الهاتف والكمبيوتر بنفس الشبكة
- ثبت Expo Go على الهاتف

## 📚 للمزيد من التفاصيل

راجع الملفات التالية:
- `README.md` - الدليل الشامل
- `quran-app/INSTRUCTIONS.md` - تعليمات مفصلة
- `quran-app/PROJECT_SUMMARY.md` - ملخص المشروع

---

**🎉 استمتع بتطبيق القرآن الكريم!**

*"وَنُنَزِّلُ مِنَ الْقُرْآنِ مَا هُوَ شِفَاءٌ وَرَحْمَةٌ لِّلْمُؤْمِنِينَ"*
